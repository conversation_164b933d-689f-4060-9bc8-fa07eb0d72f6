<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice" page-height="190mm" page-width="297mm">
            <fo:region-body margin="14mm" margin-top="10mm" />
            <fo:region-after extent="8mm"/>

        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:static-content flow-name="xsl-region-after">
            <fo:table space-before="0mm" space-after="0mm" margin-top="5mm" margin-right="5mm">
                <fo:table-column/>
                <fo:table-body>
                    <fo:table-row>
                        <fo:table-cell text-align="right" font-weight="bold" padding-top="-4mm">
                            <fo:block font-size="9pt" padding-bottom="0mm">Page No. <fo:page-number/></fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>
        </fo:static-content>
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="96%" margin-top="0mm"  border="2pt solid black" padding-top="2mm" padding="6mm">
                <fo:table border="none">
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="190mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="21pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                    CAMBRIDGE INTERNATIONAL EDUCATION
                                </fo:block>
                                <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                    LEARNING MILESTONES REPORT - 2024-25
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                         content-width="45mm" content-height="non-uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-family="Times New Roman, serif" font-size="10pt" space-before="4mm">
                    <fo:table-column column-width="36mm" />
                    <fo:table-column column-width="58mm" />
                    <fo:table-column column-width="42mm" />
                    <fo:table-column column-width="50mm" />
                    <fo:table-column column-width="16mm" />
                    <fo:table-column column-width="20mm" />
                    <fo:table-column column-width="25mm" />
                    <fo:table-column column-width="20mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold">
                                <fo:block>CANDIDATE NAME : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.name}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>GRADE FACILITATOR : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.gradeFacilitatorName}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>GRADE : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:text="${model.body.grade}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold">
                                <fo:block>CENTRE NO : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>
                                        IA697
                                    </fo:block>
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug == 'del909850'}">
                                    <fo:block>
                                        IA700
                                    </fo:block>
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug == 'del765517'}">
                                    <fo:block>
                                        IA699
                                    </fo:block>
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != 'pal454783' and model.body.orgSlug != 'del909850' and model.body.orgSlug != 'del765517'}">
                                    <fo:block>
                                        IN174
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-family="Times New Roman, serif" font-size="10pt"
                          th:if="${model.body.assessmentName != null and (model.body.assessmentName.contains('internal assessment one') or model.body.assessmentName.contains('internal assessment two'))}">
                    <fo:table-column column-width="55mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="28mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-column column-width="35mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block th:text="${#strings.toUpperCase(model.body.assessmentName)}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${#strings.toUpperCase(model.body.assessment)}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block>ATTENDANCE : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${model.body.attendance}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block>SCHOOL SECTION : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${model.body.schoolSectionName}"></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table border="none" font-family="Times New Roman, serif" font-size="10pt"
                          th:if="${model.body.assessmentName != null and (!model.body.assessmentName.contains('internal assessment one') and !model.body.assessmentName.contains('internal assessment two'))}">
                    <fo:table-column column-width="48mm" />
                    <fo:table-column column-width="47mm" />
                    <fo:table-column column-width="28mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-column column-width="35mm" />
                    <fo:table-column column-width="56mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block th:text="${#strings.toUpperCase(model.body.assessmentName)}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${#strings.toUpperCase(model.body.assessment)}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block>ATTENDANCE : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${model.body.attendance}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block>SCHOOL SECTION : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${model.body.schoolSectionName}"></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" font-family="Times New Roman, serif" font-size="10pt">
                    <fo:table-column column-width="51mm" />
                    <fo:table-column column-width="130mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-top="3mm">
                                <fo:block>CAMBRIDGE CURRICULUM : </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block th:text="${model.body.cambridgeCurriculum}"></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block font-family="Times New Roman, serif" space-before="3pt">
                    <fo:block th:text="${model.body.firstTable.title}" font-weight="bold"
                              font-size="12pt" padding-top="2mm" padding-left="10mm">
                    </fo:block>
                    <fo:table border="0.1mm solid">
                        <fo:table-column column-width="11mm" />
                        <fo:table-column column-width="100mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-header>
                            <fo:table-row font-weight="bold" text-align="center" font-size="12pt">
                                <fo:table-cell border="0.5mm solid" number-rows-spanned="2"
                                               padding="1mm" text-align="left">
                                    <fo:block>S.No</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" number-rows-spanned="2"
                                               padding="1mm" text-align="left">
                                    <fo:block>Subjects</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" number-columns-spanned="2"
                                               padding="0.9mm">
                                    <fo:block th:text="${model.body.firstTable.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" number-rows-spanned="2"
                                               padding="1mm">
                                    <fo:block>Percentage %</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" number-rows-spanned="2"
                                               padding="1mm">
                                    <fo:block>Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row text-align="center" font-size="9pt" font-weight="bold">
                                <fo:table-cell border="0.5mm solid" padding="1mm">
                                    <fo:block >Max Marks</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" padding="1mm">
                                    <fo:block >Marks Scored</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body text-align="center" font-size="10pt">
                            <fo:table-row th:each="mark,marksStat : ${model.body.firstTable.marks}">
                                <fo:table-cell border="0.5mm solid" padding="1mm" text-align="left">
                                    <fo:block th:text="${marksStat.index + 1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" padding="1mm" text-align="left" font-weight="bold" font-size="9pt">
                                    <fo:block th:text="${mark.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" padding="1mm" font-weight="bolder">
                                    <fo:block th:if="${#numbers.formatDecimal(mark.paper1.maxMarks, 1, 1) == '0.0'}">NA</fo:block>
                                    <fo:block th:if="${#numbers.formatDecimal(mark.paper1.maxMarks, 1, 1) != '0.0'}" th:text="${mark.paper1.maxMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" padding="1mm">
                                    <fo:block th:if="${#numbers.formatDecimal(mark.paper1.maxMarks, 1, 1) == '0.0'}">NA</fo:block>
                                    <fo:block th:if="${#numbers.formatDecimal(mark.paper1.maxMarks, 1, 1) != '0.0'}" th:text="${mark.paper1.maxSecured}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" padding="1mm">
                                    <fo:block th:text="${mark.percentage}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="0.5mm solid" padding="1mm">
                                    <fo:block th:text="${mark.grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-family="Times New Roman, serif" space-before="3pt">
                    <fo:block font-weight="bold" padding-top="2mm" padding-left="10mm"
                              space-before="5pt" font-size="10pt">
                        GRADE FACILITATOR'S COMMENTS :
                    </fo:block>
                    <fo:table border="0.1mm solid">
                        <fo:table-column column-width="268mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block font-size="10pt" th:text="${model.body.gradeFacilitatorComments}" >
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'dps688668' ">
                    <fo:block border-width="1mm" font-size="9pt" space-before="35"
                              font-family="Times New Roman, serif">
                        <fo:table>
                            <fo:table-column column-width="80%" />
                            <fo:table-column />
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell padding="0 1mm" font-weight="bold">
                                        <fo:block font-size="9pt" padding-bottom="1mm">Mrs. Sunitha S Rao</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block font-size="9pt" padding-bottom="1mm">Mrs. M. F. Shanti Michael Anthony</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell padding="0 1mm" font-weight="bold" padding-left="5mm">
                                        <fo:block font-size="10pt" >Sr Principal  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="left" font-weight="bold" padding-left="15mm">
                                        <fo:block font-size="10pt" padding-left="5mm">Jr Principal </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} != 'dps688668' and ${model.body.orgSlug} != 'del909850' and ${model.body.orgSlug} != 'del765517' and
                       ${model.body.orgSlug} != 'pal454783'">
                    <fo:block border-width="1mm" font-size="9pt" space-before="12"
                              font-family="Times New Roman, serif">
                        <fo:table>
                            <fo:table-column column-width="80%" />
                            <fo:table-column />
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell padding="0 1mm" font-weight="bold" padding-left="5mm">
                                        <fo:block font-size="10pt" >Sr Principal  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="left" font-weight="bold" padding-left="15mm">
                                        <fo:block font-size="10pt" padding-left="5mm">Jr Principal </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del909850'">
                    <fo:block th:replace="report-card/dps/ey1to12Signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del765517'" >
                    <fo:block th:replace="report-card/dps/ey1to12Signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'pal454783'" >
                    <fo:block th:replace="report-card/dps/ey1to12Signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- Second Page -->
    <fo:page-sequence master-reference="invoice" th:if="${model.body.secondReportTables != null and !model.body.secondReportTables.isEmpty()}">
        <fo:static-content flow-name="xsl-region-after">
            <fo:table space-before="0mm" space-after="0mm" margin-top="5mm" margin-right="5mm">
                <fo:table-column/>
                <fo:table-body>
                    <fo:table-row>
                        <fo:table-cell text-align="right" font-weight="bold" padding-top="-4mm" >
                            <fo:block font-size="9pt" padding-bottom="0mm">Page No. <fo:page-number/></fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>
        </fo:static-content>
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="96%" margin-top="0cm" border="2pt solid black"
                                padding-top="2mm" padding="6mm">
                <fo:block >
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="190mm" />
                        <fo:table-column column-width="40mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block >
                                        <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                             content-width="75px" content-height="75px"/>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="21pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                              th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                    </fo:block>
                                    <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                        CAMBRIDGE INTERNATIONAL EDUCATION
                                    </fo:block>
                                    <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                        LEARNING MILESTONES REPORT - 2024-25
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block >
                                        <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                             content-width="45mm" content-height="non-uniform" />
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.secondReportTables != null and !model.body.secondReportTables.isEmpty()}"  border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" space-before="3mm" space-after="5pt">
                    <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondReportTables[0].title}"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="46.5mm"/>
                        <fo:table-column column-width="46.5mm"/>
                        <fo:table-column column-width="46.5mm"/>
                        <fo:table-column column-width="46.5mm"/>
                        <fo:table-column column-width="46.5mm"/>
                        <fo:table-header font-size="10pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Skill of the Future</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].skillsOfFutures.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].skillsOfFutures.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].skillsOfFutures.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].skillsOfFutures.column4}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].skillsOfFutures.column5}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="left">
                                    <fo:block>Performance Record</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].performanceRecords.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].performanceRecords.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].performanceRecords.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].performanceRecords.column4}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].performanceRecords.column5}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:if="${model.body.secondReportTables[0].grades.column1} !=null">
                                <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="left">
                                    <fo:block>Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].grades.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].grades.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].grades.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].grades.column4}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondReportTables[0].grades.column5}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${model.body.secondReportTables != null and !model.body.secondReportTables.isEmpty() and #lists.size(model.body.secondReportTables) == 3}" >

                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" space-after="5pt">
                        <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondReportTables[1].title}"></fo:block>
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-header font-size="10pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].performanceRecords.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].performanceRecords.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].performanceRecords.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].performanceRecords.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].performanceRecords.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                        <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondReportTables[2].title}"></fo:block>
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-header font-size="10pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column5}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column6}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column7}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column5}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column6}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column7}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${model.body.secondReportTables != null and !model.body.secondReportTables.isEmpty() and #lists.size(model.body.secondReportTables) > 3}" >
                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" space-after="5pt">
                        <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondReportTables[1].title}"></fo:block>
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="54mm"/>
                            <fo:table-column column-width="54mm"/>
                            <fo:table-column column-width="54mm"/>
                            <fo:table-column column-width="56mm"/>
                            <fo:table-column column-width="55mm"/>
                            <fo:table-header font-size="10pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].skillsOfFutures.column5}"></fo:block>
                                    </fo:table-cell>

                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body>

                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[1].grades.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" space-after="5pt">
                        <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondReportTables[2].title}"></fo:block>
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-column column-width="54.5mm"/>
                            <fo:table-header font-size="10pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].skillsOfFutures.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].performanceRecords.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].performanceRecords.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].performanceRecords.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].performanceRecords.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].performanceRecords.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[2].grades.column5}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                        <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondReportTables[3].title}"></fo:block>
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-header font-size="10pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column5}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column6}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].skillsOfFutures.column7}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column3}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column4}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column5}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column6}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondReportTables[3].grades.column7}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>


            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:static-content flow-name="xsl-region-after">
            <fo:table space-before="0mm" space-after="0mm" margin-top="5mm" margin-right="5mm">
                <fo:table-column/>
                <fo:table-body>
                    <fo:table-row>
                        <fo:table-cell text-align="right" font-weight="bold" padding-top="-4mm" >
                            <fo:block font-size="9pt" padding-bottom="0mm">Page No. <fo:page-number/></fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>
        </fo:static-content>
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="96%" margin-top="0cm" border="2pt solid black"
                                padding-top="2mm"  padding="5mm">
                <fo:block >
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="188mm" />
                        <fo:table-column column-width="20mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block >
                                        <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                             content-width="75px" content-height="75px"/>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="21pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                              th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                    </fo:block>
                                    <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                        CAMBRIDGE INTERNATIONAL EDUCATION
                                    </fo:block>
                                    <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                        LEARNING MILESTONES REPORT - 2024-25
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block >
                                        <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                             content-width="45mm" content-height="non-uniform" />
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-family="Times New Roman, serif" margin-left="5mm">
                    <fo:block font-weight="bold"
                              font-size="11pt" padding-top="2mm" padding-left="10mm">ACADEMIC PERFORMANCE
                        GRADING RUBRIC
                    </fo:block>
                    <fo:block font-weight="bold"
                              font-size="10pt" padding-top="2mm" padding-left="10mm">CAMBRIDGE SUBJECTS
                    </fo:block>
                    <fo:block font-weight="bold"
                              font-size="10pt" padding-top="2mm" padding-left="10mm">Percentage Grade -
                        Grading Rubric
                    </fo:block>
                    <fo:block font-size="9pt" padding-top="1mm">
                        <fo:block padding="1mm">
                            A* - Outstanding - 90% - 100%
                        </fo:block>
                        <fo:block padding="1mm">
                            A - Excellent - 80% - 89%
                        </fo:block>
                        <fo:block padding="1mm">
                            B - Meritorius - 70% - 79%
                        </fo:block>
                        <fo:block padding="1mm">
                            C - Commendable - 60% - 69%
                        </fo:block>
                        <fo:block padding="1mm">
                            D - Good - 50% - 59%
                        </fo:block>
                        <fo:block padding="1mm">
                            E - Satisfactory - 40% - 49%
                        </fo:block>
                        <fo:block padding="1mm">
                            F- Fair - 30% - 39%
                        </fo:block>
                        <fo:block padding="1mm">
                            G - Needs Improvement - 20% - 29%
                        </fo:block>
                        <fo:block padding="1mm">
                            U - Ungraded - Below 20%
                        </fo:block>
                        <fo:block padding="1mm">
                            NA - Not Applicable
                        </fo:block>
                    </fo:block>
                    <fo:block font-weight="bold"
                              font-size="11pt" padding-top="1mm" padding-left="10mm">SKILLSETS GRADING
                        RUBRIC
                    </fo:block>
                    <fo:block font-weight="bold" font-size="10pt" padding-top="1mm" padding-left="10mm">
                        Cambridge Learner Attributes,Personality,Creative &amp; Professional Curricular skills &amp; Physical Education - Sports Skills
                    </fo:block>
                    <fo:block font-weight="bold"
                              font-size="10pt" padding-top="1mm" padding-left="10mm">Skillsets - Grading
                        Rubric for 10 Pointer
                    </fo:block>
                    <fo:block font-size="9pt" padding-top="1mm">
                        <fo:block padding="1mm">
                            8.1m– 10.0m -Proficient Learner
                        </fo:block>
                        <fo:block padding="1mm">
                            6.1m – 8.0m -Confident Learner
                        </fo:block>
                        <fo:block padding="1mm">
                            4.1m – 6.0m -Prominent Learner
                        </fo:block>
                        <fo:block padding="1mm">
                            2.1m - 4.0m -Progressing Learner
                        </fo:block>
                        <fo:block padding="1mm">
                            0 – 2.0m -Evolving Learner
                        </fo:block>
                        <fo:block padding="1mm">
                            N/A- Not Applicable
                        </fo:block>
                    </fo:block>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

</fo:root>