<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin-bottom="5mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" background-color="#f1f4ca"  padding="10mm">
                <fo:block-container absolute-position="absolute" top="0%" right="0%" left="0%" width="100%" height="0%" text-align="center">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial, sans-serif">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="1"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="1"/>
                                            <feFuncB type="linear" slope="1"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <!-- <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/music-logo.png"/> -->
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-top="-7mm" text-align="center" th:if="${model.body.orgSlug == 'pal332908'}">
                    <fo:external-graphic content-width="245mm" content-height="105" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS+SR+White+Header.png')"/>
                </fo:block>
                <fo:block margin-top="-7mm" text-align="center" th:if="${model.body.orgSlug == 'pal174599'}">
                    <fo:external-graphic content-width="245mm" content-height="105" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PAIS+SN+White+Header+(1).png')"/>
                </fo:block>
                <fo:block margin-top="-7mm" text-align="center" th:if="${model.body.orgSlug == 'pal556078'}">
                    <fo:external-graphic content-width="245mm" content-height="105" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PMS+TMLR+White+Header.png')"/>
                </fo:block>
                <fo:block margin-top="-7mm" text-align="center" th:if="${model.body.orgSlug == 'pal988947'}">
                    <fo:external-graphic content-width="300mm" content-height="120" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PMS+AL+White+Header.png')"/>
                </fo:block>
                <fo:block margin-top="-7mm" text-align="center" th:if="${model.body.orgSlug == 'pal454783'}">
                    <fo:external-graphic content-width="245mm" content-height="105" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS+GPT+White+Header.png')"/>
                </fo:block>
                <fo:block margin-top="-7mm" text-align="center" th:if="${model.body.orgSlug == 'pal233196'}">
                    <fo:external-graphic content-width="245mm" content-height="105" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PMS+BWP+White.png')"/>
                </fo:block>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="26pt" color="#734d00" font-family="Arial, sans-serif" font-weight="bold" text-align="center" margin-bottom="4mm" >
                                    HOLISTIC PROGRESS REPORT CARD
                                </fo:block>
                                <fo:block font-size="26pt" color="#734d00" font-family="Arial, sans-serif" font-weight="bold" text-align="center" margin-bottom="0mm" >
                                    2024-2025
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block>
                    <fo:external-graphic content-width="200mm" content-height="100%" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/music-logo.png')"/>
                </fo:block>
                <fo:table border="none" margin-top="-20mm">
                    <fo:table-column column-width="260mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="30pt" font-weight="bold" font-family="Times New Roman, Serif" text-align="center" margin-bottom="2mm" color="#000099" padding-top="-22mm"
                                          th:text="${model.header.subjectTtl}"></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block text-align="center" margin-top="2mm" padding-top="-9mm">
                    <fo:external-graphic text-align="center" content-width="120mm" content-height="250" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/school-couple.png')"/>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--second page-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" padding="10mm" background-color="#f1f4ca">
                <fo:block-container absolute-position="absolute" top="0%" right="0%" left="0%" width="100%" height="0%" text-align="center">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial, sans-serif">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="1"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="1"/>
                                            <feFuncB type="linear" slope="1"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="120mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm">
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-top="-5mm" text-align="right" margin-right="35mm">
                                    <fo:external-graphic content-width="200%" content-height="170" width="150%" height="170" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101060751245.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block margin="12mm" padding-top="-12mm">
                    <fo:block font-size="12pt"  border="1pt dashed grey" background-color="#ffffff" padding="5mm" font-family="Arial, sans-serif">
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Name of the student :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.studentName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Grade/Section :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm" >
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.gradeName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold" >Admission No :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.admissionNo}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Date of birth :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.dateOfBirth}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Class Teacher :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.classTeacher}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Father’s Name :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.fatherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Mother’s Name :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.motherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="50mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="50mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Phone Number: (F)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.fatherPhoneNo}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" padding-left="-10mm">
                                        <fo:block  font-weight="bold">(M)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.motherPhoneNo}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell  height="5mm">
                                        <fo:block  font-weight="bold">Things I like :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.thingsILike}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">I live in :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.iLiveIn}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">My friends are :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.myFriendsAre}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">My favourites :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" >
                                        <fo:block >
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Colours :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.myFavouriteColoursAre}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Foods :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm" >
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.myFavouriteFoods}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block  font-weight="bold">Games :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm" >
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.myFavouriteGames}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Animals :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm" >
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.myFavouriteAnimals}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                        <fo:table border="none" margin-bottom="2mm">
                            <fo:table-column column-width="68mm"/>
                            <fo:table-column column-width="110mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Address :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black" padding-left="-10mm">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.allAboutMe.address}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="120mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="left" margin-right="15mm" padding-top="-10mm">
                                    <fo:external-graphic content-width="100%" content-height="170" width="100%" height="170" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/school-couple2.png')" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!-- third page-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" background-color="#f1f4ca"  padding="10mm">
                <fo:block-container absolute-position="absolute" top="0%" right="0%" left="0%" width="100%" height="0%" text-align="center">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial, sans-serif">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="1"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="1"/>
                                            <feFuncB type="linear" slope="1"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="190mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="30pt" color="#734d00" font-family="Times New Roman, Serif" font-weight="bold" text-align="center" margin-bottom="2mm" >A Glimpse of Myself</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="20mm"/>
                    <fo:table-column column-width="126mm"/>
                    <fo:table-column column-width="60mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block-container border="4pt solid #5d2e12" padding="1mm" background-color="#ffffff">
                                    <fo:block-container border="1pt solid #5d2e12" padding="3mm" padding-right="-1mm" padding-left="-1mm" height="50mm" background-color="#ffffff">
                                        <fo:block display-align="center" text-align="center">
                                            <fo:instream-foreign-object content-width="140%" content-height="140%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.body.images.aGlimpseOfMySelf}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block-container>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block text-align="center" margin-top="2mm" padding-top="-18mm">
                                    <fo:external-graphic margin-left="-10mm" text-align="center" content-width="230%" content-height="230" width="230%" height="230" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img1.png')"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="190mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="30pt" color="#734d00" font-family="Times New Roman, Serif" font-weight="bold" text-align="center" margin-bottom="2mm" >A Glimpse of My Family</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="60mm"/>
                    <fo:table-column column-width="135mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block text-align="center" margin-top="2mm">
                                    <fo:external-graphic margin-right="-20mm" text-align="center" content-width="110%" content-height="200" width="110%" height="200" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img2.png')"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block-container border="4pt solid #5d2e12" padding="1mm" background-color="#ffffff">
                                    <fo:block-container border="1pt solid #5d2e12" padding="3mm" padding-right="-1mm" padding-left="-1mm" height="50mm" background-color="#ffffff">
                                        <fo:block display-align="center" text-align="center">
                                            <fo:instream-foreign-object content-width="140%" content-height="140%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.body.images.aGlimpseOfMyFamily}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block-container>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block text-align="center" padding-top="-10mm">
                    <fo:external-graphic text-align="right" content-width="90%" content-height="120" width="90%" height="120"  src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img3.png')"/>
                </fo:block>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="16pt" color="#734d00" font-family="Arial, sans-serif" font-weight="bold" text-align="center" margin-bottom="2mm" white-space="nowrap">
                                    Note: Paste a photo or draw picture of you and your family in the given
                                </fo:block>
                                <fo:block font-size="16pt" color="#734d00" font-family="Arial, sans-serif" font-weight="bold" text-align="left" margin-bottom="2mm" white-space="nowrap" margin-left="10mm">
                                    space above.
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fourth page-->
    <fo:page-sequence master-reference="invoice" >
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="98%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5mm" margin-bottom="5mm" >
                <fo:block-container absolute-position="absolute" top="30%" left="53%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="-8mm">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="0">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101110210005.png')"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="0" text-align="right">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101063207242.png')"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="10pt" font-family="Times New Roman, Serif" margin-left="2mm" >

                    <!-- Third Table -->
                    <fo:table border="1pt solid #ffffff">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" padding-bottom="5pt" height="20pt">
                                        <fo:block font-weight="bold" font-size="18pt" >Competencies</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" padding-bottom="5pt" height="20pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-1</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" padding-bottom="5pt" height="20pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-2</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="1pt solid #ffffff" th:each="competencie : ${model.body.competencies[0]}">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm"  padding-top="3pt" height="20pt">
                                        <fo:block font-weight="bold" text-align="left" font-size="16pt" th:text="${competencie.subjectName}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <th:block th:each="skill, skillStat : ${competencie.skills}">
                                <fo:table-row >
                                    <fo:table-cell number-columns-spanned="3">
                                        <fo:block-container background-color="#f4b092" border="1pt solid #ffffff"
                                                        padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                            <fo:block font-size="13pt" font-weight="bold" th:text="${skill.skillName}"></fo:block>
                                        </fo:block-container>
                                    </fo:table-cell>
                                </fo:table-row>

                                <th:block th:each="detail, stat : ${skill.details}">
                                    <fo:table-row >
                                        <fo:table-cell>
                                             <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                        <fo:block font-size="12pt" th:text="${detail.subjectValue}"></fo:block>
                                        </fo:block-container>
                                        </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term1Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term2Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            </th:block>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="left">
                                    <fo:external-graphic content-width="130%" content-height="100" width="130%" height="100" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img4.png')" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="right">
                                    <fo:external-graphic content-width="130%" content-height="100" width="130%" height="100" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img5.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>




    <fo:page-sequence master-reference="invoice" >
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="98%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5mm" margin-bottom="5mm">
                <fo:block-container absolute-position="absolute" top="30%" left="53%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="-8mm">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="0">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101110210005.png')"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="0" text-align="right">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101063207242.png')"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="10pt" font-family="Times New Roman, Serif" margin-left="2mm" >

                    <!-- Third Table -->
                    <fo:table border="1pt solid #ffffff">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" padding-bottom="5pt" height="20pt">
                                        <fo:block font-weight="bold" font-size="18pt" >Competencies</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" padding-bottom="5pt" height="20pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-1</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" padding-bottom="5pt" height="20pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-2</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="1pt solid #ffffff" th:each="competencie : ${model.body.competencies[1]}">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm"  padding-top="3pt" height="20pt">
                                        <fo:block font-weight="bold" text-align="left" font-size="16pt" th:text="${competencie.subjectName}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <th:block th:each="skill, skillStat : ${competencie.skills}">
                            <th:block th:each="detail, stat : ${skill.details}">
                            <fo:table-row >
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                        <fo:block font-size="12pt" th:text="${detail.subjectValue}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term1Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term2Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            </th:block>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="left">
                                    <fo:external-graphic content-width="130%" content-height="100" width="130%" height="100" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img4.png')" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="right">
                                    <fo:external-graphic content-width="130%" content-height="100" width="130%" height="100" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img5.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice" >
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="98%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5mm" margin-bottom="5mm">
                <fo:block-container absolute-position="absolute" top="30%" left="53%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="-8mm">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="0">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101110210005.png')"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="0" text-align="right">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101063207242.png')"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="10pt" font-family="Times New Roman, Serif" margin-left="2mm" >

                    <!-- Third Table -->
                    <fo:table border="1pt solid #ffffff">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" padding-bottom="5pt" height="20pt">
                                        <fo:block font-weight="bold" font-size="18pt" >Competencies</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="20pt" padding-bottom="5pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-1</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="20pt" padding-bottom="5pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-2</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="1pt solid #ffffff" th:each="competencie : ${model.body.competencies[2]}">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm"  padding-top="3pt" height="20pt">
                                        <fo:block font-weight="bold" text-align="left" font-size="16pt" th:text="${competencie.subjectName}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <th:block th:each="skill, skillStat : ${competencie.skills}">
                            <th:block th:each="detail, stat : ${skill.details}">
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                        <fo:block font-size="12pt" th:text="${detail.subjectValue}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term1Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term2Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            </th:block>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="1pt solid #ffffff" th:each="competencie : ${model.body.competencies[3]}">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm"  padding-top="3pt" height="20pt">
                                        <fo:block font-weight="bold" text-align="left" font-size="16pt" th:text="${competencie.subjectName}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <th:block th:each="skill, skillStat : ${competencie.skills}">
                            <th:block th:each="detail, stat : ${skill.details}">
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                        <fo:block font-size="12pt" th:text="${detail.subjectValue}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term1Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term2Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            </th:block>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="202mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="center">
                                    <fo:external-graphic content-width="100%" content-height="250" width="100%" height="250" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/image6.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice" >
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="98%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5mm" margin-bottom="5mm">
                <fo:block-container absolute-position="absolute" top="30%" left="53%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="-8mm">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="0">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101110210005.png')"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="0" text-align="right">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101063207242.png')"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="10pt" font-family="Times New Roman, Serif" margin-left="2mm" >

                    <!-- Third Table -->
                    <fo:table border="1pt solid #ffffff">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" padding-bottom="5pt" height="20pt">
                                        <fo:block font-weight="bold" font-size="18pt" >Competencies</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="20pt" padding-bottom="5pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-1</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="20pt" padding-bottom="5pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-2</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="1pt solid #ffffff" th:each="competencie : ${model.body.competencies[4]}">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm"  padding-top="3pt" height="20pt">
                                        <fo:block font-weight="bold" text-align="left" font-size="16pt" th:text="${competencie.subjectName}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <th:block th:each="skill, skillStat : ${competencie.skills}">
                            <th:block th:each="detail, stat : ${skill.details}">
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                        <fo:block font-size="12pt" th:text="${detail.subjectValue}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term1Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term2Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            </th:block>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="202mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="center">
                                    <fo:external-graphic content-width="100%" content-height="250" width="100%" height="250" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/image8.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice" th:if="${ #lists.size(model.body.competencies) > 5}">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="98%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5mm" margin-bottom="5mm">
                <fo:block-container absolute-position="absolute" top="30%" left="53%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="-8mm">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="0">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101110210005.png')"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="0" text-align="right">
                                    <fo:external-graphic content-width="100%" content-height="70" width="100%" height="70" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241101063207242.png')"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="10pt" font-family="Times New Roman, Serif" margin-left="2mm" >

                    <!-- Third Table -->
                    <fo:table border="1pt solid #ffffff">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" padding-bottom="5pt" height="20pt">
                                        <fo:block font-weight="bold" font-size="18pt" >Competencies</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="20pt" padding-bottom="5pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-1</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="20pt" padding-bottom="5pt" margin-left="-1.5mm">
                                        <fo:block font-weight="bold" font-size="18pt" >Term-2</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="1pt solid #ffffff" th:each="competencie : ${model.body.competencies[5]}">
                        <fo:table-column column-width="138mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="32mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block-container background-color="#ebb391" border="1pt solid #ffffff" padding-left="2mm"  padding-top="3pt" height="20pt">
                                        <fo:block font-weight="bold" text-align="left" font-size="16pt" th:text="${competencie.subjectName}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <th:block th:each="skill, skillStat : ${competencie.skills}">
                            <th:block th:each="detail, stat : ${skill.details}">
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt" height="28pt">
                                        <fo:block font-size="12pt" th:text="${detail.subjectValue}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term1Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                                        border="1pt solid #ffffff" text-align="center" padding-top="5pt" height="28pt" margin-left="-1.5mm">
                                        <fo:block font-size="12pt" padding-top="2mm" th:text="${detail.term2Value}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            </th:block>
                            </th:block>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="101mm"/>
                    <fo:table-column column-width="101mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="left">
                                    <fo:external-graphic content-width="130%" content-height="100" width="130%" height="100" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img4.png')" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="right">
                                    <fo:external-graphic content-width="130%" content-height="100" width="130%" height="100" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/img5.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- fifth page-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-6mm" padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5pt">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-bottom="6mm" font-size="34pt"  font-weight="600" text-align="center" color="#734d00" font-family="Times New Roman, serif" >
                    Learner’s profile by the teacher
                </fo:block>
                <fo:block margin-bottom="3mm" font-size="14pt" font-weight="bold" text-align="left" color="#734d00" font-family="Times New Roman, serif" margin-left="6mm" margin-right="3mm">
                    Teacher must present a narrative summary of the child, Highlighting the strenghths, challenges and suggestions for improvement.
                </fo:block>
                <fo:block margin-bottom="3mm" font-size="26pt" font-weight="600" text-align="center" color="#734d00" font-family="Times New Roman, serif">
                    Observation
                </fo:block>
                <fo:block font-size="10pt" font-family="Arial, Serif" margin-bottom="6mm" margin-left="2mm">
                    <fo:table border="0">
                        <fo:table-column column-width="120mm"/>
                        <fo:table-column column-width="82mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:table border="0">
                                        <fo:table-column column-width="120mm"/>
                                        <fo:table-body>
                                            <fo:table-row margin-bottom="3mm">
                                                <fo:table-cell>
                                                    <fo:table border="0">
                                                        <fo:table-column column-width="120mm"/>
                                                        <fo:table-body>
                                                            <fo:table-row>
                                                                <fo:table-cell>
                                                                    <fo:block font-weight="600" font-size="18pt" color="#734d00" >Term-1</fo:block>
                                                                </fo:table-cell>
                                                            </fo:table-row>
                                                            <fo:table-row >
                                                                <fo:table-cell>
                                                                    <fo:block>
                                                                        <fo:inline>
                                                                            <fo:block-container>
                                                                                <fo:block font="14pt" th:text="${model.body.observations.term1}">
                                                                                </fo:block>
                                                                            </fo:block-container>
                                                                        </fo:inline>
                                                                    </fo:block>
                                                                </fo:table-cell>
                                                            </fo:table-row>
                                                            <fo:table-row>
                                                                <fo:table-cell>
                                                                    <fo:block padding-top="10mm" font-weight="600" font-size="18pt" color="#734d00" >Term-2</fo:block>
                                                                </fo:table-cell>
                                                            </fo:table-row>
                                                            <fo:table-row >
                                                                <fo:table-cell>
                                                                    <fo:block>
                                                                        <fo:inline>
                                                                            <fo:block-container>
                                                                                <fo:block font="14pt" th:text="${model.body.observations.term2}">
                                                                                </fo:block>
                                                                            </fo:block-container>
                                                                        </fo:inline>
                                                                    </fo:block>
                                                                </fo:table-cell>
                                                            </fo:table-row>
                                                        </fo:table-body>
                                                    </fo:table>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">
                                        <fo:external-graphic content-width="60mm" content-height="90%" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241102034747179.png')"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block margin-bottom="3mm" font-size="32pt" font-weight="600" text-align="center" color="#734d00" font-family="Times New Roman, serif">Parent’s Feedback</fo:block>
                <fo:block font-size="10pt" font-family="Times New Roman, Serif" margin-left="2mm">
                    <fo:table border="1px solid #ffffff">
                        <fo:table-column column-width="68mm"/>
                        <fo:table-column column-width="68mm"/>
                        <fo:table-column column-width="66mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container background-color="#F1C9B2" border="1pt solid #ffffff" padding-left="2mm" margin-right="-1mm" height="28" padding-top="5pt">
                                        <fo:block font-weight="600" font-size="18pt" >Aspect</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#F1C9B2" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" height="28" padding-top="5pt">
                                        <fo:block  font-weight="600" text-align="center" font-size="18pt" >Term-1</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container background-color="#F1C9B2" border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" height="28" padding-top="5pt">
                                        <fo:block  font-weight="600" text-align="center" font-size="18pt" >Term-2</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <!-- Third Table -->
                    <fo:table border="1pt solid #ffffff" th:each="competencie, stat : ${model.body.parentsFeedback}">
                        <fo:table-column column-width="68mm"/>
                        <fo:table-column column-width="68mm"/>
                        <fo:table-column column-width="68mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                               border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt">
                                    <fo:block-container>
                                        <fo:block font-weight="bold" font-size="13pt" th:text="${competencie.name}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                               border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt">
                                    <fo:block-container>
                                        <fo:block font-weight="bold" font-size="13pt" th:text="${competencie.term1}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell th:attr="background-color=${stat.index % 2 == 0 ? '#f9e6db' : '#F1C9B2'}"
                                               border="1pt solid #ffffff" padding-left="2mm" padding-right="2mm" padding-top="5pt">
                                    <fo:block-container>
                                        <fo:block font-weight="bold" font-size="13pt" th:text="${competencie.term2}"></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--six page-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" background-color="#f1f4ca"  padding="10mm" margin-top="-6mm">
                <fo:block-container absolute-position="absolute" top="0%" right="0%" left="0%" width="100%" height="0%" text-align="center">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial, sans-serif">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="1"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="1"/>
                                            <feFuncB type="linear" slope="1"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="190mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="34pt" color="#734d00" font-family="Times New Roman, serif" font-weight="bold" text-align="center" margin-bottom="2mm" >Learners Portfolio</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block margin-top="3mm" font-size="14pt" font-weight="bold" text-align="left" color="#734d00" font-family="Times New Roman, serif" margin-left="6mm" margin-right="3mm">
                    Note: Paste pictures / display selected work done by student in various experiential and inter disciplinary tasks done in class.
                </fo:block>
                <fo:table border="none" margin-top="3mm" margin-left="9mm">
                    <fo:table-column column-width="190mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block-container border="4pt solid #5d2e12" padding="1mm" margin-left="0" background-color="white">
                                    <fo:block-container border="1pt solid #5d2e12" padding="3mm" padding-right="-1mm" padding-left="1mm" height="50mm" background-color="white">
                                        <fo:block display-align="center" text-align="center">
                                            <fo:instream-foreign-object content-width="140%" content-height="140%">
                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                     width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                    <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                                           th:xlink:href="@{${model.body.images.learnersPortFolio}}"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block-container>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" margin-top="3mm">
                    <fo:table-column column-width="190mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="26pt" color="#734d00" font-family="Times New Roman, serif" font-weight="bold" text-align="center" margin-bottom="2mm" >Growth Chart</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block-container margin-top="5mm" margin-bottom="6mm">


                    <!-- Container for Two Terms Side-by-Side -->
                    <fo:table width="100%" table-layout="fixed" border="none" padding-left="10pt">
                        <fo:table-column column-width="35%"/>
                        <fo:table-column column-width="28%"/>
                        <fo:table-column column-width="35%"/>
                        <fo:table-body >
                            <fo:table-row>
                                <!-- Term-1 -->
                                <fo:table-cell>
                                    <fo:block-container height="100pt" width="90%" background-color="#f2f7fc" padding="10pt" border="1px solid black" margin-left="5mm" >
                                        <fo:block font-family="Arial, sans-serif" font-size="10pt" font-weight="bold" text-align="left" margin-bottom="5mm">
                                            <fo:inline>Term–1</fo:inline>
                                        </fo:block>
                                        <fo:block font-family="Arial, sans-serif" font-size="10pt" text-align="left" margin-bottom="4mm">
                                            <fo:inline>My height is</fo:inline>
                                            <fo:inline padding-left="5mm" padding-right="5mm" border-bottom="1px solid #5d2e12" th:text="${model.body.heightAndWeight.term1Height}"></fo:inline>
                                            <fo:inline>cms.</fo:inline>
                                        </fo:block>
                                        <fo:block font-family="Arial, sans-serif" font-size="10pt" text-align="left">
                                            <fo:inline>My weight is</fo:inline>
                                            <fo:inline padding-left="5mm" padding-right="5mm" border-bottom="1px solid #5d2e12" th:text="${model.body.heightAndWeight.term1Weight}"></fo:inline>
                                            <fo:inline>kgs.</fo:inline>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block>
                                            <fo:inline></fo:inline>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:table-cell>

                                <!-- Term-2 -->
                                <fo:table-cell >
                                    <fo:block-container height="100pt" width="90%" background-color="#f2f7fc" padding="10pt" border="1px solid black"  >
                                        <fo:block font-family="Arial, sans-serif" font-size="10pt" font-weight="bold" text-align="left" margin-bottom="5mm">
                                            <fo:inline>Term–2</fo:inline>
                                        </fo:block>
                                        <fo:block font-family="Arial, sans-serif" font-size="10pt" text-align="left" margin-bottom="4mm">
                                            <fo:inline>My height is</fo:inline>
                                            <fo:inline padding-left="5mm" padding-right="5mm" border-bottom="1px solid #5d2e12" th:text="${model.body.heightAndWeight.term2Height}"></fo:inline>
                                            <fo:inline>cms.</fo:inline>
                                        </fo:block>
                                        <fo:block font-family="Arial, sans-serif" font-size="10pt" text-align="left">
                                            <fo:inline>My weight is</fo:inline>
                                            <fo:inline padding-left="5mm" padding-right="5mm" border-bottom="1px solid #5d2e12" th:text="${model.body.heightAndWeight.term2Weight}"></fo:inline>
                                            <fo:inline>kgs.</fo:inline>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>


                <fo:block text-align="center" margin-top="2mm">
                    <fo:external-graphic text-align="center" content-width="100%" content-height="210" width="100%" height="210" src="url('https://images.wexledu.com/live-worksheets/elp561041/20241109045423329.png')"/>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5pt">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-bottom="3mm" font-size="30pt" font-weight="600" text-align="center" color="#734d00" font-family="Times New Roman, serif" >Self-Assessment</fo:block>
                <fo:block margin-bottom="3mm" font-size="14pt"  text-align="left" color="#734d00" font-family="Times New Roman, serif" margin-left="3mm" margin-right="3mm">
                    Self reflection on inter-disciplinary activity done by the child. Example: clay work, drawing,
                    playing a game, colouring, puppet-making, model making, etc.
                </fo:block>
                <fo:block font-size="10pt" width="96%" font-family="Arial, Serif" text-align="center" margin-left="2%" margin-right="2%" border="1px solid #dd8047" margin-bottom="6mm">
                    <fo:table background-color="#f9e6db">
                        <fo:table-column column-width="40%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell  text-align="left" number-columns-spanned="3" border-bottom="1pt solid #dd8047" border-right="1pt solid #dd8047">
                                    <fo:block  padding="5pt" font-size="16pt" >The teacher must help the children to fill this sheet: </fo:block>
                                    <fo:block padding="5pt" font-size="16pt" >
                                        (For young children, teachers may fill based on observation and discussion)
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell text-align="right" border-bottom="1pt solid #dd8047" border-right="1pt solid #dd8047" number-columns-spanned="2">
                                    <fo:block padding="5pt" font-weight="bold" font-size="16pt" >Term-1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" border-bottom="1pt solid #dd8047" border-right="1pt solid #dd8047">
                                    <fo:block padding="5pt" font-weight="bold" font-size="16pt" >Term-2</fo:block>
                                </fo:table-cell>
                            </fo:table-row>]
                            <fo:table-row th:each="values : ${model.body.selfAssessment}">
                                <fo:table-cell text-align="left" border-bottom="1pt solid #dd8047" border-right="1pt solid #dd8047">
                                    <fo:block padding="5pt" font-size="14pt" th:text="${values.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" border-bottom="1pt solid #dd8047" border-right="1pt solid #dd8047">
                                    <fo:block padding="5pt" font-size="14pt"  th:text="${values.term1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" border-bottom="1pt solid #dd8047" border-right="1pt solid #dd8047">
                                    <fo:block padding="5pt" font-size="14pt"  th:text="${values.term2}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block margin-bottom="2mm" font-size="30pt" font-weight="600" text-align="center" color="#94b6db" font-family="Times New Roman, serif" >Peer-Assessment</fo:block>
                <fo:block font-size="10pt" width="96%" font-family="Arial, Serif" text-align="center" margin-left="2%" margin-right="2%" border="1px solid #94b6db" margin-bottom="6mm">
                    <fo:table background-color="#eaf1f8">
                        <fo:table-column column-width="40%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell  text-align="left" number-columns-spanned="3" border-bottom="1pt solid #94b6db" border-right="1pt solid #94b6db">
                                    <fo:block  padding="5pt" font-size="16pt" >Peer feedback from classmate (s)</fo:block>
                                    <fo:block padding="5pt" font-size="16pt" >Collaborative game/activity such as coloring together, playing a game, etc.</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row th:each="values : ${model.body.peerAssessment}">
                                <fo:table-cell text-align="left" border-bottom="1pt solid #94b6db" border-right="1pt solid #94b6db">
                                    <fo:block padding="5pt" font-size="14pt" th:text="${values.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" border-bottom="1pt solid #94b6db" padding-top="9pt">
                                    <fo:block margin-bottom="2mm" text-align="center" font-size="0">
                                        <fo:block font-size="12pt" th:if="${values.term1 == 0}">
                                            <fo:external-graphic content-width="22px" content-height="22px" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241029103749623.png')" />
                                        </fo:block>
                                        <fo:block font-size="12pt" th:if="${values.term1 == 1}">
                                            <fo:external-graphic content-width="22px" content-height="22px" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241029104232440.png')" />
                                        </fo:block>
                                        <fo:block font-size="12pt" th:if="${values.term1 == 2 }">
                                            <fo:external-graphic content-width="22px" content-height="22px" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/icon3.png')" />
                                        </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" border-bottom="1pt solid #94b6db" padding-top="9pt" border="1px solid #94b6db">
                                    <fo:block margin-bottom="2mm" text-align="center" font-size="0">
                                        <fo:block font-size="12pt" th:if="${values.term2 == 0}">
                                            <fo:external-graphic content-width="22px" content-height="22px" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241029103749623.png')" />
                                        </fo:block>
                                        <fo:block font-size="12pt" th:if="${values.term2 == 1}">
                                            <fo:external-graphic content-width="22px" content-height="22px" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241029104232440.png')" />
                                        </fo:block>
                                        <fo:block font-size="12pt" th:if="${values.term2 == 2 }">
                                            <fo:external-graphic content-width="22px" content-height="22px" src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/icon3.png')" />
                                        </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--table-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" background-color="#f1f4ca"  padding="10mm" padding-left="2mm">
                <fo:block th:if="${model.body.gradeSlug == 'nur' || model.body.gradeSlug == 'lkg' || model.body.gradeSlug == 'ukg'}">
                    <fo:block font-size="16pt" font-family="Times New Roman, Serif" font-weight="bold" text-align="center">ACADEMIC PERFORMANCE</fo:block>
                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" margin-left="0.1cm">
                        <fo:block font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                        <fo:table border="1pt solid black" >
                            <fo:table-column column-width="35mm" />
                            <fo:table-column column-width="17mm" />
                            <fo:table-column column-width="17mm" />
                            <fo:table-column column-width="14mm" />
                            <fo:table-column column-width="14mm" />
                            <fo:table-column column-width="11mm" />
                            <fo:table-column column-width="17mm" />
                            <fo:table-column column-width="17mm" />
                            <fo:table-column column-width="14mm" />
                            <fo:table-column column-width="14mm" />
                            <fo:table-column column-width="11mm" />
                            <fo:table-column column-width="14mm" />
                            <fo:table-column column-width="11mm" />



                            <fo:table-header font-size="9pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black"  font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="2" padding-top="9mm">
                                        <fo:block font-size="12pt">SUBJECT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="5">
                                        <fo:block font-size="12pt">TERM-I</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="5">
                                        <fo:block font-size="12pt">TERM-II</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="2">
                                        <fo:block font-size="12pt">OverAll</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>

                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>Oral-Reflective Practices (Grading)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>Reflective Practices-1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" text-align="center" padding="1mm">
                                        <fo:block>Term-1 </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>RP-1  + Term-1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block>Grade</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>Oral-Reflective Practices (Grading)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>Reflective Practices-1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block>Term-2 </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>RP-2  + Term-2</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block>Grade</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>Term1 + Term2</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block>Grade</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>

                            <fo:table-body >
                                <fo:table-row th:each="marks : ${model.body.academicPerformance.table1}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="left">
                                        <fo:block th:text="${marks.subjectName}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.orp}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.rp}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.term1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.rpandterm1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.grade}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.orpTerm2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.rpTerm2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.term2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.rpandterm2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.gradeTerm2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.term1AndTerm2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.term1AndTerm2Grade}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block border-width="1mm" font-size="9pt" margin-left="4cm" font-family="Times New Roman, serif" space-before="20mm">
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="40mm" />
                            <fo:table-column column-width="40mm" />
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center" number-columns-spanned="2">
                                        <fo:block  >TERM-I</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  >TERM-2</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-size="7pt">
                                <fo:table-row th:each="marks : ${model.body.academicPerformance.table2}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold" margin-left="0cm">
                                        <fo:block th:text="${marks.subjectName}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${marks.term}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${marks.term2}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                    <fo:block font-size="16pt" font-family="Times New Roman, Serif"  text-align="center" space-before="10mm" font-weight="bold">ATTENDANCE</fo:block>
                    <fo:block border-width="1mm" font-size="9pt" margin-left="0.5cm" font-family="Times New Roman, serif" >
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  > Month </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>March</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>April</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>May</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>June</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>July</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Aug</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Sep</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Oct</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Nov</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Dec</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Jan</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Feb</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-size="9pt">
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold" margin-left="0cm">
                                        <fo:block >No. of Working days</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwMarch}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwApril}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwMay}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwJune}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwJuly}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwAug}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwSep}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwOct}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwNov}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwDec}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwJan}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwFeb}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold" margin-left="0cm">
                                        <fo:block >No. present</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npMarch}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npApril}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npMay}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJune}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJuly}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npAug}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npSep}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npOct}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npNov}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npDec}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJan}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npFeb}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold" margin-left="0cm">
                                        <fo:block >% of Attendance</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npMarchAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npAprilAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npMayAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJuneAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJulyAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npAugAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-right="1pt solid black" padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npSepAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npOctAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npNovAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npDecAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJanAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.npFebAttendancePercentage}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block font-size="16pt" font-family="Times New Roman, Serif"  text-align="center" space-before="10mm" font-weight="bold">Signature with date</fo:block>
                    <fo:block border-width="1mm" font-size="9pt" margin-left="1.4cm" font-family="Times New Roman, serif" >
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="30mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  >Term</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  >Parent/Guardian</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>PP-Incharge</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-size="9pt">
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold" margin-left="0cm">
                                        <fo:block >Term 1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                            <fo:table-body  font-size="9pt">
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold" margin-left="0cm">
                                        <fo:block >Term 2</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${model.body.gradeSlug != 'nur' && model.body.gradeSlug != 'lkg' && model.body.gradeSlug != 'ukg'}" padding-top="-8mm">
                    <fo:block font-size="16pt" font-family="Times New Roman, Serif" font-weight="bold"  text-align="center">ACADEMIC PERFORMANCE</fo:block>
                    <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                        <fo:block font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                        <fo:table border="1pt solid black" margin-left="2mm">
                            <fo:table-column column-width="38.5mm" />
                            <fo:table-column column-width="19mm" />
                            <fo:table-column column-width="19mm" />
                            <fo:table-column column-width="18mm" />
                            <fo:table-column column-width="17mm" />
                            <fo:table-column column-width="13mm" />
                            <fo:table-column column-width="19mm" />
                            <fo:table-column column-width="21mm" />
                            <fo:table-column column-width="23mm" />
                            <fo:table-column column-width="17mm" />

                            <fo:table-header font-size="9pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="3" padding-top="5mm">
                                        <fo:block>SUBJECT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" font-size="12pt" padding="1mm" number-columns-spanned="9">
                                        <fo:block>TERM-1</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="3">
                                        <fo:block>PT-1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="3">
                                        <fo:block>PT-2</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="3">
                                        <fo:block></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>PEN PAPER TEST</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>C.W/H.W</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>SE/MA </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>PEN PAPER TEST</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>C.W/H.W</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>SE/MA</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>HALF YEARLY EXAM  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>TOTAL (100M) PT-1+PT-2+HE </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>GRADE</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>

                            <fo:table-body font-size="9pt">
                                <fo:table-row th:each="marks : ${model.body.academicPerformance.table1}">
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.subjectName}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.penAndPaper1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.cwOrhw1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.seOrma1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.penAndPaper2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.cwOrhw2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.seOrma2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.hye}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.total}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.grade}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block th:if="${model.body.academicPerformance.table4 != null and #lists.size(model.body.academicPerformance.table4) > 0}"
                             space-before="3mm" border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                        <fo:block font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                        <fo:table border="1pt solid black" margin-left="2mm">
                            <fo:table-column column-width="30mm" />
                            <fo:table-column column-width="16mm" />
                            <fo:table-column column-width="13mm" />
                            <fo:table-column column-width="13mm" />
                            <fo:table-column column-width="16mm" />
                            <fo:table-column column-width="13mm" />
                            <fo:table-column column-width="13mm" />
                            <fo:table-column column-width="14mm" />
                            <fo:table-column column-width="21mm" />
                            <fo:table-column column-width="15mm" />
                            <fo:table-column column-width="23mm" />
                            <fo:table-column column-width="17mm" />

                            <fo:table-header font-size="9pt">
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" number-rows-spanned="3" padding-top="5mm">
                                        <fo:block>SUBJECT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" font-size="12pt" padding="1mm" number-columns-spanned="11">
                                        <fo:block>TERM-2</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="3">
                                        <fo:block>PT-3</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="3">
                                        <fo:block>PT-4</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="5">
                                        <fo:block></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>PEN PAPER TEST</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>C.W/H.W</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>SE/MA </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>PEN PAPER TEST</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>C.W/H.W</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>SE/MA</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>FINAL EXAM  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>TOTAL(100M) PT-3 + PT-4 + FE </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>GRADE</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>OVERALL (TERM-1 + TERM-2) %</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                        <fo:block>OVERALL GRADING</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>

                            <fo:table-body font-size="9pt">
                                <fo:table-row th:each="marks : ${model.body.academicPerformance.table4}">
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.subjectName}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.penAndPaper1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.cwOrhw1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.seOrma1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.penAndPaper2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.cwOrhw2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.seOrma2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.hye}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.total}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.grade}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.overallTerm1AndTerm2Percentage}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${marks.overallGrading}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                    <fo:block border-width="1mm" font-size="9pt" margin-left="3cm" font-family="Times New Roman, serif" space-before="3mm">
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block  >SUBJECT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>TERM-1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>TERM-2</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-size="9pt">
                                <fo:table-row th:each="marks : ${model.body.academicPerformance.table2}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                        <fo:block th:text="${marks.subjectName}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${marks.term}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${marks.term2}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                    <fo:block font-size="16pt" font-family="Times New Roman, Serif"  text-align="center" space-before="3mm" font-weight="bold">ATTENDANCE</fo:block>
                    <fo:block border-width="1mm" font-size="9pt" margin-left="0.5cm" font-family="Times New Roman, serif" >
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-column column-width="12mm" />
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  > Month </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>March</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>April</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>May</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>June</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>July</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Aug</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Sep</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Oct</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Nov</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Dec</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Jan</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Feb</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-size="9pt">
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                        <fo:block >No. of Working days</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwMarch}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwApril}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwMay}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwJune}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwJuly}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwAug}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwSep}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwOct}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwNov}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwDec}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwJan}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.nwFeb}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                        <fo:block >No. present</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npMarch}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npApril}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npMay}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJune}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJuly}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npAug}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npSep}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npOct}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npNov}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npDec}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npJan}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block th:text="${model.body.academicPerformance.table3.npFeb}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                        <fo:block >% of Attendance</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.overallPercentage1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-right="1pt solid black" padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block th:text="${model.body.academicPerformance.table3.overallPercentage2}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding="1mm" margin-left="0cm" text-align="center" >
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                    <fo:block font-size="16pt" font-family="Times New Roman, Serif"  text-align="center" space-before="3mm" font-weight="bold">Signature with date</fo:block>
                    <fo:block border-width="1mm" font-size="9pt" margin-left="1.4cm" font-family="Times New Roman, serif" >
                        <fo:table border="1pt solid black">
                            <fo:table-column column-width="30mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-header>
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  >TERM</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold"  padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block  >Parent/Guardian</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-size="9pt">
                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                        <fo:block >Term-1</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row >
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                        <fo:block >Term-2</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                        <fo:block ></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5pt">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block th:if="${model.body.gradeSlug != 'nur' && model.body.gradeSlug != 'lkg' && model.body.gradeSlug != 'ukg'}"
                          font-size="10pt" width="94%" font-family="Arial, Serif" text-align="center" margin-left="5%" margin-right="5%" border="1px solid #fff" margin-bottom="6mm">
                    <fo:table background-color="#f2cdb6">
                        <fo:table-column column-width="18%"/>
                        <fo:table-column column-width="15%"/>
                        <fo:table-column column-width="17%"/>
                        <fo:table-column column-width="17%"/>
                        <fo:table-column column-width="17%"/>
                        <fo:table-column column-width="16%"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell  text-align="center" number-columns-spanned="6" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block font-weight="600" padding="5pt" font-size="20pt" >Grading Scale</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row font-weight="bold">
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="13pt">
                                    <fo:block padding="5pt" >Term-I &amp; II</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >A (91-100) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >B (75-90) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt" >
                                    <fo:block padding="5pt" >C (56-74) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >D (35-55) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >NA (0-34) </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.gradeSlug == 'nur' || model.body.gradeSlug == 'lkg' || model.body.gradeSlug == 'ukg'}"
                          font-size="10pt" width="94%" font-family="Arial, Serif" text-align="center" margin-left="3%" margin-right="3%" border="1px solid #fff" margin-bottom="6mm">
                    <fo:table background-color="#f2cdb6">
                        <fo:table-column column-width="13%"/>
                        <fo:table-column column-width="15%"/>
                        <fo:table-column column-width="14%"/>
                        <fo:table-column column-width="14%"/>
                        <fo:table-column column-width="14%"/>
                        <fo:table-column column-width="14%"/>
                        <fo:table-column column-width="16%"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell  text-align="center" number-columns-spanned="7" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block font-weight="600" padding="5pt" font-size="20pt" >Grading Scale</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row font-weight="bold">
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="13pt">
                                    <fo:block padding="5pt" >Term-1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >O-Outstanding (45-50) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >E-Excelling (38-44) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt" >
                                    <fo:block padding="5pt" >A-Achieving (28-37) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >D-Developing (18-27) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >B-Beginning (0-17) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" >NA-Not Applicable</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-weight="bold">
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="13pt">
                                    <fo:block padding="5pt" >Term-II</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >O-Outstanding (90-100) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >E-Excelling (75-89) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt" >
                                    <fo:block padding="5pt" >A-Achieving (56-74) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >D-Developing (35-55) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff" padding-top="8pt">
                                    <fo:block padding="5pt" >B-Beginning  (0-35) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="center" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" >NA-Not Applicable</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-size="10pt" width="80%" font-family="Arial, Serif" text-align="center" margin-left="5%" margin-right="5%" border="1px solid #fff" margin-bottom="6mm">
                    <fo:table background-color="#f2cdb6">
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="70%"/>
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell  text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block font-weight="600" padding="5pt" font-size="18pt" >Level</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block font-weight="600" padding="5pt" font-size="18pt" >Interpretation</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" font-weight="600" font-size="16pt" >Beginner</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" font-size="13pt" >Tries to achieve the competency and associated Learning Outcomes with a lot of support from teachers.</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" font-weight="600" font-size="16pt" >Progressing</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" font-size="13pt" >Achieves the Competency and associated Learning Outcomes with occasional/some support from teachers.</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" font-weight="600" font-size="16pt" >Proficient</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" border-bottom="1pt solid #fff" border-right="1pt solid #fff">
                                    <fo:block padding="5pt" font-size="13pt">Achieves the Competency and associated Learning Outcomes on his/her own.</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="202mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-bottom="2mm" text-align="center">
                                    <fo:external-graphic content-width="100%" content-height="250" width="100%" height="250" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241030075450044.png')" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%">
                <fo:block-container absolute-position="absolute" top="0" left="0" right="0">
                    <fo:block text-align="center" font-family="Arial">
                        <fo:instream-foreign-object content-width="28cm" content-height="100cm">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://images.wexledu.com/live-worksheets/wexl-internal/20241109105802923.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block margin-top="7cm">
                    <fo:table border="none" text-align="center" margin-left="10mm">
                        <fo:table-column column-width="210mm"/>
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block font-size="23pt" color="#803c0a" font-weight="600" text-align="left" >ALL CHILDREN ARE UNIQUE</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block font-size="23pt" color="#803c0a" font-weight="600" text-align="left" >AND HAVE A DIFFERENT WAY</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block font-size="23pt" color="#803c0a" font-weight="600" text-align="left" >AND PACE OF LEARNING</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:table border="none" text-align="center">
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="70%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block font-size="18pt" color="#803c0a" font-weight="600" >- National curriculum Framework </fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block></fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block-container>
                                        <fo:block font-size="18pt" color="#803c0a" font-weight="600" >for Foundational stage 2024</fo:block>
                                    </fo:block-container>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%"  padding="16mm" background-color="#f1f4ca" space-before="5mm" space-after="5pt">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="100%" content-height="100%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block text-align="center" font-size="14pt" font-family="Arial">
                    <fo:table border="none">
                        <fo:table-column column-width="202mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block margin-bottom="2mm" text-align="center">
                                        <fo:external-graphic content-width="100%" content-height="250" width="100%" height="250" src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20241030073301432.png')" />
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:block margin-bottom="3mm" font-size="32pt" font-weight="600" text-align="center" color="#734d00">OUR BRANCHES</fo:block>
                    <fo:table border="0" margin-left="7mm">
                        <fo:table-column column-width="67mm"/>
                        <fo:table-column column-width="67mm"/>
                        <fo:table-column column-width="68mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Bowenpally</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7032993299</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Boduppal</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7660002469</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Gandipet</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7093331170</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Alwal</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7286094427</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Secunderabad</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">9573469759</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Saroornagar</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">9100188822</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Tirumalagiri</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7288820006</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Bachupally</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7670907771</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Kesara</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">7660002460</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600"></fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left"></fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left"></fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600">&#x2022;</fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">Sagar Road</fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left" font-weight="600">8897021999</fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:list-block>
                                        <fo:list-item >
                                            <fo:list-item-label>
                                                <fo:block text-align="left" font-size="16pt" font-weight="600"></fo:block>
                                            </fo:list-item-label>
                                            <fo:list-item-body>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left"></fo:block>
                                                <fo:block padding="1.5mm" margin-left="5mm" font-size="14pt" text-align="left"></fo:block>
                                            </fo:list-item-body>
                                        </fo:list-item>
                                    </fo:list-block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>