<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="16mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="8mm" >
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%" padding-top="-1mm">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter2">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7.5" />
                                            <feFuncG type="linear" slope="7.5"/>
                                            <feFuncB type="linear" slope="7.5"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-Model-School-Favicon_5.png"/>
                                <image filter="url(#brightnessFilter2)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none"  >
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="-4mm">
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal332908' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal332908'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="14">SAGAR ROAD, HYDERABAD</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="18">GANDIPET</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm"
                                          th:if="${model.body.orgSlug == 'del217242'}">
                                    DELHI PUBLIC SCHOOL
                                    <fo:block font-size="18" padding-top="-2mm" padding-bottom="6mm">SANTOSHNAGAR</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug != 'pal332908' and model.body.orgSlug != 'pal454783' and model.body.orgSlug != 'del217242' }"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != 'dps688668'}" font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt" padding-top="-5mm">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block-container width="100%" height="100%" margin-top="0cm"  th:if="${model.body.orgSlug == 'dps688668'}">
                                    <fo:block border-width="1mm" font-size="8pt" space-before="2mm"
                                              font-family="Times New Roman, serif" space-after="2mm">
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center">
                                            Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,
                                        </fo:block>
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center">
                                            UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076</fo:block>
                                    </fo:block>
                                </fo:block-container>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.isoData}"></fo:block>
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">
                                    TERM-I
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="0pt">Academic Year : 2024 - 25</fo:block>
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != 'pal454783'}">
                                    <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="0pt"
                                          th:text="${model.header.academicYear}">
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif"  space-after="2pt" padding-top="-9mm">
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Class &amp; Section &#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.mothersName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="3pt">
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Father's/Guardian's Name&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" th:text="${model.body.fathersName}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="15mm"  />
                        <fo:table-column column-width="61.5mm" />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="28mm"  />

                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2" text-align="center" padding-top="4mm">
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left" number-rows-spanned="2" padding-top="4mm">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  number-columns-spanned="4" >
                                    <fo:block >TERM-I</fo:block>
                                </fo:table-cell>


                            </fo:table-row>
                            <fo:table-row >

                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column4}"> </fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pa1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pa2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.hye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1total}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="5" text-align="left"  font-weight="bold" >
                                    <fo:block>OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="2.5mm"
                th:if="${model.body.thirdTable.marks != null and #lists.size(model.body.thirdTable.marks) > 0}">

                    <fo:table border="1pt solid black" >
                    <fo:table-column column-width="15mm" />
                    <fo:table-column column-width="61.5mm" />
                        <fo:table-column column-width="75mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-body  font-family="Times New Roman, serif">
                        <fo:table-row th:each="external : ${model.body.thirdTable.marks}" >
                            <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${external.sno}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                <fo:block th:text="${external.subject}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" font-size="8pt" padding="1mm" text-align="left">
                                <fo:block th:text="${external.term1description}"></fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding="1mm">
                                <fo:block th:text="${external.term1grade}"></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!--  2nd table starts -->
                <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="2.5mm"
                          th:if="${model.body.firstTable.external != null and #lists.size(model.body.firstTable.external) > 0}">


                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="15mm" />
                        <fo:table-column column-width="61.5mm" />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="28mm"  />

                        <fo:table-body  font-family="Times New Roman, serif">
                            <fo:table-row th:each="external : ${model.body.firstTable.external}" >
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${external.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm"  >
                                    <fo:block th:text="${external.pa1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.pa2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.hye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.grade2}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- third Table -->
                <fo:block border-width="1mm" font-size="9pt"  space-before="6" text-align="center" font-family="Times New Roman, serif"
                          th:if="${model.body.secondTable.marks != null and #lists.size(model.body.secondTable.marks) > 0}">
                    <fo:block th:text="${model.body.secondTable.title}"  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif"></fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="100mm" />
                        <fo:table-column column-width="79mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>TERM-I</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt"  space-before="4" text-align="center" font-family="Times New Roman, serif"
                          th:if="${model.body.fourthTable[0].term1Grade != null && model.body.fourthTable[0].term1Grade.trim() != ''}" >
                    <fo:block  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif">
                        [ On a 4-Point (A<fo:inline vertical-align="super">+</fo:inline> to C) grading scale ]
                    </fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="100mm" />
                        <fo:table-column column-width="79mm" />
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block >PHYSICAL EDUCATION</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.fourthTable[0].term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Attendance -->
                <fo:block th:if="${ model.body.orgSlug != 'pal454783' }">
                <fo:block border-width="1mm" font-size="10pt" space-after="3pt" font-family="Times New Roman, serif" space-before="2mm">
                    <fo:block font-weight="bold" space-after="3pt" font-family="Times New Roman, serif">Attendance&#160;:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="35mm" />
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="39mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body font-family="Times New Roman, serif" >
                            <fo:table-row space-after="5pt">
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Total Working Days&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Days Present&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Attendance %:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                </fo:block>

                <fo:block border-width="1mm" font-size="10pt" space-before="1mm" space-after="1mm">
                    <fo:table border="none">
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block font-weight="bold">Remarks&#160;:</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block>
                    <fo:inline>
                        <fo:block-container font-size="9pt">
                            <fo:block border-bottom="0.2mm solid black" th:text="${model.body.attendance.remarks}">
                            </fo:block>
                        </fo:block-container>
                    </fo:inline>
                </fo:block>

                <!-- Signature Block-->
                <fo:block space-before="28pt" th:if="${model.body.orgSlug == 'dps688668' and (model.body.gradeSlug == 'i' or model.body.gradeSlug == 'ii')}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block space-before="28pt" th:if="${model.body.orgSlug == 'dps688668' and (model.body.gradeSlug == 'iii' or model.body.gradeSlug == 'iv')}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Vice Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block space-before="28pt" th:if="${model.body.orgSlug == 'dps688668' and model.body.gradeSlug == 'v'}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Sr. Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug} == 'del909850'">
                    <fo:block th:replace="report-card/dps/signatureForMahendra.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del217242'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del765517'">
                    <fo:block th:replace="report-card/dps/nadergulSignature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del189476'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'pal332908'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} != 'dps688668' and ${model.body.orgSlug} != 'del909850' and ${model.body.orgSlug} != 'del217242'
                    and ${model.body.orgSlug} != 'del765517' and ${model.body.orgSlug} != 'del189476' and ${model.body.orgSlug} != 'pal332908' ">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <!-- Report Card Result Table -->
                <fo:block border-width="1mm" font-size="10pt" padding-top="-5mm" space-before="1mm" font-family="Times New Roman, serif" >
                    <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.gradingScale}"></fo:block>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-before="2mm" >
                    <fo:block  font-size="8"  font-family="Times New Roman, serif">PT : PERIODIC TEST, HYE : HALF YEARLY</fo:block>
                </fo:block>
                <fo:block th:if="${model.body.firstTable.subjectGradeSlug} == 'english-i' or  ${model.body.firstTable.subjectGradeSlug} == 'english-ii'  or ${model.body.firstTable.subjectGradeSlug} == 'mathmatics-i' or ${model.body.firstTable.subjectGradeSlug} == 'mathmatics-ii'">
                    <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.subjectGradeSlug}"></fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>


