package com.wexl.mgcv.feeslite.repository;

import com.wexl.mgcv.feeslite.dto.MgcvStudent;
import com.wexl.mgcv.feeslite.model.MgcvFeesLite;
import com.wexl.retail.model.Student;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface MgcvFeesLiteRepository extends JpaRepository<MgcvFeesLite, Long> {

  MgcvFeesLite findByStudent(Student student);

  @Query(
      value =
          """
                    select u.id as userId, s.id as studentId, u.user_name as username, u.first_name as firstName,
                  u.last_name as lastName,s.roll_number as rollNumber,s.class_roll_number as classRollNumber,
                  sfl.term1status as term1Status,sfl.term2status as term2Status,
                  s.academic_year_slug as academicYear
                  from users u
                  inner join students s on s.user_id=u.id
                  left join mgcv_fees_lite sfl on sfl.student_id=s.id
                  left join student_attribute_values sav on sav.student_id=s.id and attribute_definition_id=3
                  where s.section_id=:section and u.organization=:orgSlug and u.deleted_at is null
                  """,
      nativeQuery = true)
  List<MgcvStudent> getMgcvStudentsBySection(String orgSlug, Long section);

  @Query(
      value =
          """
                          select u.id as userId, s.id as studentId, u.user_name as username, u.first_name as firstName,
                          u.last_name as lastName, s.class_id as classId,s.board_id as boardId,s.class_roll_number as classRollNumber,
                          s.roll_number as rollNumber,sfl.term1status as term1Status,sfl.term2status as term2Status,
                          sfl.term1paid_date as term1Date,sfl.term2paid_date as term2Date,sec.name as sectionName,
                          s.academic_year_slug as academicYear from users u
                          inner join students s on s.user_id=u.id
                          left join  mgcv_fees_lite sfl on sfl.student_id=s.id
                  left join student_attribute_values sav on sav.student_id=s.id and attribute_definition_id=3
                  left join sections sec on s.section_id = sec.id
                   where u.user_name=:authUserId and u.organization=:orgSlug""",
      nativeQuery = true)
  List<MgcvStudent> getMgcvStudentByAuthUserId(String orgSlug, String authUserId);
}
