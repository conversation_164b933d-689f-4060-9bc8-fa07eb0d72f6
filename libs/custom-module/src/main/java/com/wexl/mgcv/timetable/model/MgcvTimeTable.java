package com.wexl.mgcv.timetable.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "mgcv_timetables")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MgcvTimeTable extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "section_uuid")
  private String sectionUuid;

  @Column(name = "file_path")
  private String filePath;
}
