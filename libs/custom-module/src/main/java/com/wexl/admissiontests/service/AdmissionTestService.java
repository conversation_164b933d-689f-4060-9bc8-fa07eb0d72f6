package com.wexl.admissiontests.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.admissiontests.dto.AdmissionTestDto;
import com.wexl.admissiontests.dto.AdmissionTestMetricResult;
import com.wexl.admissiontests.model.AdmissionTests;
import com.wexl.admissiontests.repository.AdmissionTestRepository;
import com.wexl.dps.dto.BetReportDto;
import com.wexl.dps.reportcard.BetReportCard;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.dto.MobileNumberLoginDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.teacher.training.controller.SimpleDataControllerHelper;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.whatsapp.WhatsAppService;
import com.wexl.retail.whatsapp.interakt.dto.Request;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmissionTestService {

  private final ValidationUtils validationUtils;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestService scheduleTestService;
  private final DateTimeUtil dateTimeUtil;
  private final AdmissionTestRepository admissionTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final StrapiService strapiService;
  private final BetReportCard betReportCard;
  private final UserRepository userRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final CurriculumService curriculumService;
  private final StorageService storageService;
  private final SimpleDataControllerHelper simpleDataControllerHelper;
  private final WhatsAppService whatsAppService;
  private final MessageTemplateRepository messageTemplateRepository;
  private final AuthService authService;
  private final OrganizationRepository organizationRepository;

  public AdmissionTestDto.Response createTest(
      String orgSlug, AdmissionTestDto.Request request, String studentAuthId) {

    validationUtils.isOrgValid(orgSlug);
    var testDefinitionList =
        testDefinitionRepository.getTestsByGradeAndOrgSlugAndType(TestType.MOCK_TEST.name());

    var testDefinitionData =
        testDefinitionList.stream()
            .filter(x -> x.getGradeSlug().equals(request.gradeSlug()))
            .sorted(Comparator.comparing(TestDefinition::getId).reversed())
            .toList();

    if (testDefinitionData.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "TestDefinition is not available");
    }

    var testDefinition = testDefinitionData.get(0);
    var user = validationUtils.isValidUser(studentAuthId);
    var scheduleRequest = buildScheduleTestRequest(testDefinition, user, request);
    var testSchedule = scheduleTestService.scheduleBetTest(testDefinition, scheduleRequest, user);
    var admissionTest =
        admissionTestRepository.save(
            buildAdmissionTest(orgSlug, testDefinition, testSchedule, request, studentAuthId));

    return buildResponse(admissionTest, testSchedule, user);
  }

  private AdmissionTestDto.Response buildResponse(
      AdmissionTests admissionTest, ScheduleTest scheduleTest, User user) {

    var test =
        scheduleTestStudentRepository.findAllTestsForStudent(
            user.getId(), List.of(TestType.MOCK_TEST.name()));

    var currentTest =
        test.stream()
            .filter(x -> x.getScheduleTestId() == scheduleTest.getId())
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "Current test not found"));

    return AdmissionTestDto.Response.builder()
        .testDefinitionId(admissionTest.getTestDefinitionId())
        .scheduleTestId(admissionTest.getTestScheduleId())
        .startDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .endDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .status(
            scheduleTestService.getScheduledTestStatus(
                scheduleTest.getStartDate(), scheduleTest.getEndDate()))
        .subjectName(null)
        .testName(scheduleTest.getTestDefinition().getTestName())
        .testState(currentTest.getTestState())
        .testType(TestType.MOCK_TEST.name())
        .scheduleTestUuid(currentTest.getScheduleTestUuid())
        .category(scheduleTest.getTestDefinition().getCategory().getValue())
        .admissionTestId(admissionTest.getId())
        .build();
  }

  private SimpleScheduleTestRequest buildScheduleTestRequest(
      TestDefinition testDefinition, User user, AdmissionTestDto.Request request) {
    var student = user.getStudentInfo();

    return SimpleScheduleTestRequest.builder()
        .testDefinitionId(testDefinition.getId())
        .allStudents(false)
        .message("All the Best")
        .startDate(dateTimeUtil.convertTimeStampToLong(Timestamp.valueOf(LocalDateTime.now())))
        .endDate(
            dateTimeUtil.convertTimeStampToLong(
                Timestamp.valueOf(LocalDateTime.now().plusHours(3))))
        .duration(15)
        .metadata(buildMetaData(student))
        .studentIds(Collections.singleton(student.getId()))
        .build();
  }

  private ScheduleTestMetadata buildMetaData(Student student) {
    var section = student.getSection();
    return ScheduleTestMetadata.builder()
        .board(section.getBoardSlug())
        .grade(section.getGradeSlug())
        .sections(Collections.singletonList(section.getName()))
        .build();
  }

  private AdmissionTests buildAdmissionTest(
      String orgSlug,
      TestDefinition testDefinition,
      ScheduleTest testSchedule,
      AdmissionTestDto.Request request,
      String studentAuthId) {

    return AdmissionTests.builder()
        .name(request.name())
        .admissionNo(request.admissionNo())
        .testDefinitionId(testDefinition.getId())
        .testScheduleId(testSchedule.getId())
        .authUserId(studentAuthId)
        .phoneNumber(request.phoneNumber())
        .orgSlug(orgSlug)
        .status(TestStudentStatus.PENDING)
        .gradeName(request.gradeName())
        .gradeSlug(request.gradeSlug())
        .location(request.location())
        .emailId(request.emailId())
        .parentName(request.parentName())
        .schoolName(request.schoolName())
        .referer(request.referer())
        .build();
  }

  public List<Grade> getGradesByTestDefinition(String orgSlug) {
    var testDefinitionList =
        testDefinitionRepository.getTestsByGradeAndOrgSlugAndType(TestType.MOCK_TEST.name());

    var gradeSlug =
        testDefinitionList.stream().map(TestDefinition::getGradeSlug).distinct().toList();
    var orgCurriculum = curriculumService.getBoardsHierarchy(orgSlug);
    var curriculumGrades =
        orgCurriculum.stream()
            .filter(board -> board.getSlug().equals("bet"))
            .flatMap(board -> board.getGrades().stream().map(Grade::getSlug))
            .toList();
    var commonGrades = gradeSlug.stream().filter(curriculumGrades::contains).toList();
    List<com.wexl.retail.content.model.Grade> allGrades = strapiService.getAllGrades();
    List<com.wexl.retail.content.model.Grade> grades =
        allGrades.stream()
            .filter(grade -> commonGrades.contains(grade.getSlug()))
            .distinct()
            .toList();

    return buildGrades(grades);
  }

  private List<com.wexl.retail.model.Grade> buildGrades(
      List<com.wexl.retail.content.model.Grade> gradeEntities) {
    return gradeEntities.isEmpty()
        ? Collections.emptyList()
        : gradeEntities.stream()
            .map(
                grade -> {
                  return com.wexl.retail.model.Grade.builder()
                      .id(grade.getId())
                      .slug(grade.getSlug())
                      .name(grade.getName())
                      .orderId(grade.getOrder())
                      .build();
                })
            .sorted(Comparator.comparing(com.wexl.retail.model.Grade::getOrderId))
            .toList();
  }

  public List<AdmissionTestDto.AdmissionTestsResponse> getAdmissionTests(
      String orgSlug,
      Long fromDate,
      Long toDate,
      boolean showTestDetails,
      String referer,
      String location,
      Integer limit) {
    var userDetails = authService.getUserDetails();
    if (Constants.WEXL_INTERNAL.equals(userDetails.getOrganization())
        && Objects.nonNull(fromDate)) {
      return getAllAdmissionTests(fromDate, toDate, showTestDetails, referer, limit);
    }
    var org = validationUtils.isOrgValid(orgSlug);
    List<AdmissionTests> admissionTests;
    if (StringUtils.isNotBlank(referer) && StringUtils.isNotBlank(location)) {
      admissionTests =
          admissionTestRepository.getAdmissionsByOrgSlugLocationAndReferer(
              orgSlug, location, referer);
    } else {
      admissionTests = admissionTestRepository.findAllByOrgSlugOrderByCreatedAtDesc(orgSlug);
    }
    if (admissionTests.isEmpty()) {
      return Collections.emptyList();
    }
    return admissionTests.stream()
        .map(test -> buildAdmissionTestsResponse(test, org, showTestDetails))
        .toList();
  }

  private AdmissionTestDto.AdmissionTestsResponse buildAdmissionTestsResponse(
      AdmissionTests test, Organization org, boolean showTestDetails) {
    var user = userRepository.findByAuthUserId(test.getAuthUserId()).orElseThrow();
    var testSchedule = scheduleTestService.validateTestSchedule(test.getTestScheduleId());
    BetReportDto.Body reportCard = null;
    if (showTestDetails
        && Objects.equals(
            testSchedule.getScheduleTestStudent().getFirst().getStatus(),
            TestStudentStatus.COMPLETED.name())) {
      reportCard = betReportCard.buildBody(user.getStudentInfo(), testSchedule, null);
    }
    TestStudentStatus testStudentStatus =
        switch (testSchedule.getScheduleTestStudent().getFirst().getStatus()) {
          case "PENDING" -> TestStudentStatus.NOT_STARTED;
          case "STARTED" -> TestStudentStatus.PENDING;
          default -> TestStudentStatus.COMPLETED;
        };
    return AdmissionTestDto.AdmissionTestsResponse.builder()
        .testDefinitionId(test.getTestDefinitionId())
        .studentName(test.getName())
        .studentAuthId(test.getAuthUserId())
        .organizationName(org.getName())
        .createdAt(DateTimeUtil.convertIso8601ToEpoch(test.getCreatedAt().toLocalDateTime()))
        .scheduleTestId(test.getTestScheduleId())
        .gradeSlug(test.getGradeSlug())
        .gradeName(test.getGradeName())
        .phoneNumber(test.getPhoneNumber())
        .emailId(test.getEmailId())
        .location(test.getLocation())
        .parentName(test.getParentName())
        .schoolName(test.getSchoolName())
        .testDetails(
            reportCard == null
                ? AdmissionTestDto.BetTestDetails.builder().build()
                : buildBetSectionDetails(reportCard))
        .id(test.getId())
        .admissionNo(test.getAdmissionNo() == null ? null : test.getAdmissionNo())
        .status(testStudentStatus)
        .testCenter(test.getLocation())
        .referer(test.getReferer())
        .build();
  }

  private AdmissionTestDto.BetTestDetails buildBetSectionDetails(BetReportDto.Body reportCard) {
    return AdmissionTestDto.BetTestDetails.builder()
        .section1Name(reportCard.section1Name())
        .section1marks(reportCard.section1Value())
        .section2Name(reportCard.section2Name())
        .section2marks(reportCard.section2Value())
        .section3Name(reportCard.section3Name())
        .section3marks(reportCard.section3Value())
        .section4Name(reportCard.section4Name())
        .section4marks(reportCard.section4Value())
        .overAllScore(reportCard.scoreValue())
        .build();
  }

  public S3FileUploadResult uploadProfileImage(
      String orgSlug, Long admissionTestId, String imageName, String type) {
    try {
      var admissionTest =
          admissionTestRepository
              .findById(admissionTestId)
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "AdmissionTest not found"));
      String uploadPath =
          createProfileImageUploadPath(
              orgSlug, admissionTest.getName().replace(" ", "_"), imageName, type);
      admissionTest.setProfileImage(uploadPath);
      admissionTestRepository.save(admissionTest);
      return S3FileUploadResult.builder()
          .path(uploadPath)
          .url(storageService.generatePreSignedUrlForUpload(uploadPath))
          .build();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Unable to upload image", e);
    }
  }

  public String createProfileImageUploadPath(
      String orgSlug, String userName, String imageName, String imageType) {
    return "%s/%s/%s/%s/%s.%s"
        .formatted(orgSlug, "admission-tests", "profile-images", userName, imageName, imageType);
  }

  public AdmissionTestDto.AdmissionTestSummary getStartAdmissionTestSummary(String orgSlug) {
    return AdmissionTestDto.AdmissionTestSummary.builder()
        .grades(getGradesByTestDefinition(orgSlug))
        .testCenters(fetchAdmissionTestCenters(orgSlug))
        .build();
  }

  private List<String> fetchAdmissionTestCenters(String orgSlug) {
    try {
      var optionalFile =
          simpleDataControllerHelper.getFileInLocaleFolder("bet-admission-test-centers.json");
      if (optionalFile.isEmpty()) {
        return Collections.emptyList();
      }
      var admissionTestCenters = new ObjectMapper().readValue(optionalFile.get(), Map.class);
      if (Objects.isNull(admissionTestCenters)) {
        return Collections.emptyList();
      }
      var testCenters = (List<String>) admissionTestCenters.get(orgSlug);
      if (!CollectionUtils.isEmpty(testCenters)) {
        return testCenters;
      }
      var organization = validationUtils.isOrgValid(orgSlug);
      return List.of(organization.getName());
    } catch (Exception e) {
      log.error("Error while fetching admission test centers {}", e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  @Async
  @Transactional
  public void sendWhatsAppMessage(Exam exam) {
    var admissionTests =
        admissionTestRepository.findByTestScheduleId(exam.getScheduleTest().getId());
    if (Objects.nonNull(admissionTests)) {
      admissionTests.setReportCardLink(
          constructBetReportCardPath(admissionTests.getOrgSlug(), exam.getRef()));
      sendWhatsAppMessage(admissionTests);
    }
  }

  private void sendWhatsAppMessage(AdmissionTests admissionTests) {
    try {
      var optionalMessageTemplate =
          messageTemplateRepository.findByWhatsAppTemplateIdAndOrgSlug(
              "admission_report_card", Constants.WEXL_INTERNAL);

      optionalMessageTemplate.ifPresent(
          messageTemplate -> {
            if (Objects.isNull(admissionTests.getReportCardLink())) {
              return;
            }
            var mobileNumber =
                authService.validateMobileNumber(
                    MobileNumberLoginDto.MobileNumberLoginOtpRequest.builder()
                        .countryCode("IN")
                        .mobileNumber(admissionTests.getPhoneNumber())
                        .build());

            var recipient =
                Request.Recipient.builder()
                    .date(new SimpleDateFormat("dd-MM-yyyy").format(new Date()))
                    .mobileNumber(mobileNumber)
                    .name(admissionTests.getName())
                    .orgName(validationUtils.isOrgValid(admissionTests.getOrgSlug()).getName())
                    .reportCardLink(admissionTests.getReportCardLink())
                    .build();

            var response =
                whatsAppService.sendBulkWhatsAppMessage(
                    messageTemplate.getWhatsAppTemplateId(), List.of(recipient));
            admissionTests.setRemarks(response);
            log.info(response, admissionTests.getPhoneNumber());
          });
    } catch (Exception e) {
      admissionTests.setRemarks("Failed to send WhatsApp message:  %S".formatted(e.getMessage()));
      log.error(
          "Failed to send WhatsApp message to {} for admission test completion",
          admissionTests.getPhoneNumber(),
          e);
    } finally {
      admissionTests.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
      admissionTestRepository.save(admissionTests);
    }
  }

  private String constructBetReportCardPath(String orgSlug, String ref) {
    return String.format("%s/bet-report-card/%s", orgSlug, ref);
  }

  public List<AdmissionTestDto.AdmissionTestsResponse> getAllAdmissionTests(
      Long fromDateEpoch,
      Long toDateEpoch,
      boolean showTestDetails,
      String referer,
      Integer limit) {
    var userDetails = authService.getUserDetails();
    if (!Constants.WEXL_INTERNAL.equals(userDetails.getOrganization())) {
      return Collections.emptyList();
    }
    var fromDate = dateTimeUtil.convertEpochToIso8601(fromDateEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(toDateEpoch);

    List<AdmissionTests> admissionTestsList;
    if (StringUtils.isNotEmpty(referer)) {
      admissionTestsList =
          admissionTestRepository
              .findAllByRefererAndCreatedAtBetweenAndDeletedAtIsNullOrderByCreatedAtDesc(
                  referer,
                  Timestamp.valueOf(fromDate.with(LocalTime.MIN)),
                  Timestamp.valueOf(toDate.with(LocalTime.MAX)));
    } else if (Objects.nonNull(limit)) {
      admissionTestsList =
          admissionTestRepository.findAllByCreatedAtBetweenAndDeletedAtIsNullOrderByCreatedAtDesc(
              Timestamp.valueOf(fromDate.with(LocalTime.MIN)),
              Timestamp.valueOf(toDate.with(LocalTime.MAX)),
              PageRequest.of(0, limit));
    } else {
      admissionTestsList =
          admissionTestRepository.findAllByCreatedAtBetweenAndDeletedAtIsNullOrderByCreatedAtDesc(
              Timestamp.valueOf(fromDate.with(LocalTime.MIN)),
              Timestamp.valueOf(toDate.with(LocalTime.MAX)));
    }
    if (admissionTestsList.isEmpty()) {
      return Collections.emptyList();
    }
    var admissionTestOrgs =
        admissionTestsList.stream().map(AdmissionTests::getOrgSlug).distinct().toList();
    var organizations =
        organizationRepository.findAllBySlugIn(admissionTestOrgs).stream()
            .collect(Collectors.toMap(Organization::getSlug, Function.identity()));

    return admissionTestsList.stream()
        .map(
            test ->
                buildAdmissionTestsResponse(
                    test, organizations.get(test.getOrgSlug()), showTestDetails))
        .sorted(
            Comparator.comparing(
                    (AdmissionTestDto.AdmissionTestsResponse r) ->
                        Objects.nonNull(r.testDetails())
                                && Objects.nonNull(r.testDetails().overAllScore())
                            ? r.testDetails().overAllScore()
                            : 0.0)
                .reversed())
        .toList();
  }

  public List<GenericMetricResponse> getBranchesMetric(String orgSlug) {
    var organizationSlug = validationUtils.isOrgValid(orgSlug).getSlug();
    var branchesMetric = admissionTestRepository.getBranchesMetric(organizationSlug, null);
    if (branchesMetric.isEmpty()) return Collections.emptyList();

    var weekMetricMap =
        admissionTestRepository.getAWeekMetric(organizationSlug).stream()
            .collect(Collectors.groupingBy(AdmissionTestMetricResult::getBranch));

    var today = LocalDate.now();
    var dateKeys =
        IntStream.rangeClosed(0, 6).mapToObj(i -> today.minusDays(i).toString()).toList();

    return branchesMetric.stream()
        .map(
            metric -> {
              Map<String, Object> data = new HashMap<>();
              data.put("branch_name", metric.getBranch());
              data.put("total_count", metric.getAdmissionCount());

              var dateToCountMap =
                  weekMetricMap.getOrDefault(metric.getBranch(), List.of()).stream()
                      .collect(
                          Collectors.toMap(
                              AdmissionTestMetricResult::getTestDate,
                              AdmissionTestMetricResult::getAdmissionCount,
                              Long::sum));

              var weeklyMetrics =
                  dateKeys.stream()
                      .map(
                          date ->
                              AdmissionTestDto.WeeklyMetric.builder()
                                  .date(date)
                                  .admissionCount(dateToCountMap.getOrDefault(date, 0L))
                                  .build())
                      .collect(Collectors.toList());

              data.put("weekly_metric", weeklyMetrics);
              return GenericMetricResponse.builder().data(data).build();
            })
        .collect(Collectors.toList());
  }

  public List<GenericMetricResponse> getAdmissionTestRefererMetric(String org, String branch) {
    var organizationSlug = validationUtils.isOrgValid(org).getSlug();
    var refererMetric =
        admissionTestRepository.getTestRefererMetric(
            organizationSlug, Objects.equals("Default", branch) ? null : branch);
    if (refererMetric.isEmpty()) {
      return Collections.emptyList();
    }
    var weekMetricMap =
        admissionTestRepository.getRefererWeekMetric(organizationSlug, branch).stream()
            .collect(Collectors.groupingBy(AdmissionTestMetricResult::getAdmissionReferer));

    var today = LocalDate.now();
    var dateKeys =
        IntStream.rangeClosed(0, 6).mapToObj(i -> today.minusDays(i).toString()).toList();

    return refererMetric.stream()
        .map(
            metric -> {
              Map<String, Object> data = new HashMap<>();
              data.put("referer", metric.getAdmissionReferer());
              data.put("total_count", metric.getAdmissionCount());
              data.put("branch", metric.getBranch());

              var dateToCountMap =
                  weekMetricMap.getOrDefault(metric.getAdmissionReferer(), List.of()).stream()
                      .collect(
                          Collectors.toMap(
                              AdmissionTestMetricResult::getTestDate,
                              AdmissionTestMetricResult::getAdmissionCount,
                              Long::sum));

              var weeklyMetrics =
                  dateKeys.stream()
                      .map(
                          date ->
                              AdmissionTestDto.WeeklyMetric.builder()
                                  .date(date)
                                  .admissionCount(dateToCountMap.getOrDefault(date, 0L))
                                  .build())
                      .collect(Collectors.toList());

              data.put("weekly_metric", weeklyMetrics);
              return GenericMetricResponse.builder().data(data).build();
            })
        .toList();
  }

  public List<GenericMetricResponse> getAdmissionTestByTopMetrics(String org) {
    var organization = validationUtils.isOrgValid(org);
    var branchesMetrics = admissionTestRepository.getBranchesMetric(organization.getSlug(), 5);
    var refererMetric =
        admissionTestRepository.getTopAdmissionsByReferer(organization.getSlug(), 10);
    List<AdmissionTestDto.MetricResult> metricResults = new ArrayList<>();

    var branchesMetricResponse =
        branchesMetrics.stream()
            .map(
                metric ->
                    AdmissionTestDto.MetricResult.builder()
                        .branchName(metric.getBranch())
                        .count(metric.getAdmissionCount())
                        .build())
            .toList();

    var refererMetricResponse =
        refererMetric.stream()
            .map(
                metric ->
                    AdmissionTestDto.MetricResult.builder()
                        .branchName(metric.getBranch())
                        .referer(metric.getAdmissionReferer())
                        .count(metric.getAdmissionCount())
                        .build())
            .toList();

    Map<String, Object> data = new HashMap<>();
    data.put("branches_metric", branchesMetricResponse);
    data.put("referer_metric", refererMetricResponse);
    return List.of(GenericMetricResponse.builder().data(data).build());
  }
}
