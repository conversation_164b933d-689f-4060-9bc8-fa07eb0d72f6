package com.wexl.dps.academics.service;

import com.wexl.dps.academics.dto.DpsAcademicsData;
import com.wexl.dps.academics.repository.DpsAcademicsRepository;
import com.wexl.dps.academics.repository.DpsSubjectsMetaDataRepository;
import com.wexl.dps.mlp.dto.MlpDto;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DpsAcademicsService {

  private static final List<String> GRADES_ORDER =
      Arrays.asList("i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x");

  private final DpsAcademicsRepository dpsAcademicsRepository;
  private final CurriculumService curriculumService;
  private final DpsSubjectsMetaDataRepository dpsSubjectsMetaDataRepository;
  private final SectionService sectionService;
  private final AuthService authService;
  private final UserRoleHelper userRoleHelper;
  private final TeacherOrgsService teacherOrgsService;
  private static final String UNKNOWN = "Unknown";

  public List<MlpDto.Subject> getAcademicsData(
      String orgSlug, String boardSlug, String gradeSlug, String childOrg) {
    User user = authService.getUserDetails();
    if (Boolean.TRUE.equals(userRoleHelper.isManager(user))) {
      if (childOrg != null) {
        return getAdminAcademicsData(childOrg, boardSlug, gradeSlug);
      }
      return getManagerAcademicsData(boardSlug, gradeSlug);
    }

    return getAdminAcademicsData(orgSlug, boardSlug, gradeSlug);
  }

  public List<MlpDto.Subject> getAdminAcademicsData(
      String orgSlug, String boardSlug, String gradeSlug) {
    if (gradeSlug == null) {
      return buildAcademicsDataResponse(boardSlug, orgSlug);
    }
    return buildAcademicsByGradeResponse(orgSlug, gradeSlug, boardSlug);
  }

  private List<MlpDto.Subject> buildAcademicsByGradeResponse(
      String orgSlug, String gradeSlug, String boardSlug) {
    List<DpsAcademicsData> academicsData =
        dpsAcademicsRepository.getDpsAcademicsDataByGrade(orgSlug, boardSlug, gradeSlug);
    if (academicsData.isEmpty()) {
      return Collections.emptyList();
    }

    List<SectionEntityDto.Response> sections =
        sectionService.getSectionsByGrade(orgSlug, gradeSlug);
    Map<String, List<DpsAcademicsData>> groupedBySubject =
        academicsData.stream()
            .filter(data -> data.getSubjectSlug() != null)
            .collect(Collectors.groupingBy(DpsAcademicsData::getSubjectSlug));

    Map<UUID, String> sectionMap =
        sections.stream()
            .filter(x -> x.boardSlug().equals(boardSlug))
            .collect(
                Collectors.toMap(SectionEntityDto.Response::uuid, SectionEntityDto.Response::name));

    List<SubjectsMetaData> dpsSubjectsList =
        dpsSubjectsMetaDataRepository.findByOrgSlugAndCategoryEnumAndSubjectsTypeEnum(
            orgSlug,
            SubjectsCategoryEnum.SCHOLASTIC.name(),
            SubjectsTypeEnum.MANDATORY.name(),
            boardSlug);
    Set<String> subjectSlugs =
        dpsSubjectsList.stream()
            .filter(x -> x.getGradeSlug().equals(gradeSlug))
            .map(SubjectsMetaData::getWexlSubjectSlug)
            .collect(Collectors.toSet());

    return subjectSlugs.stream()
        .map(
            subjectSlug ->
                createSubjectResponse(
                    subjectSlug,
                    groupedBySubject,
                    sections,
                    sectionMap,
                    dpsSubjectsList,
                    gradeSlug))
        .toList();
  }

  private MlpDto.Subject createSubjectResponse(
      String subjectSlug,
      Map<String, List<DpsAcademicsData>> groupedBySubject,
      List<SectionEntityDto.Response> sections,
      Map<UUID, String> sectionMap,
      List<SubjectsMetaData> dpsSubjectsList,
      String gradeSlug) {
    Optional<SubjectsMetaData> subjectMetaData =
        dpsSubjectsList.stream()
            .filter(
                x ->
                    x.getWexlSubjectSlug().equals(subjectSlug)
                        && x.getGradeSlug().equals(gradeSlug))
            .findFirst();

    List<MlpDto.Sections> sectionsList =
        sections.stream()
            .map(
                section ->
                    createSectionResponse(section, groupedBySubject, subjectSlug, sectionMap))
            .filter(Objects::nonNull)
            .toList();

    return MlpDto.Subject.builder()
        .name(subjectMetaData.map(SubjectsMetaData::getName).orElse(UNKNOWN))
        .slug(subjectSlug)
        .sections(sectionsList)
        .build();
  }

  private MlpDto.Sections createSectionResponse(
      SectionEntityDto.Response section,
      Map<String, List<DpsAcademicsData>> groupedBySubject,
      String subjectSlug,
      Map<UUID, String> sectionMap) {

    var sName = sectionMap.getOrDefault(section.uuid(), UNKNOWN);
    if (UNKNOWN.equals(sName)) {
      return null;
    }

    double knowledgePercentage =
        groupedBySubject.getOrDefault(subjectSlug, Collections.emptyList()).stream()
            .filter(data -> data.getSectionUuid().equals(section.uuid().toString()))
            .mapToDouble(DpsAcademicsData::getKnowledgePercentage)
            .findFirst()
            .orElse(0.0);

    return MlpDto.Sections.builder()
        .sectionName(sName)
        .sectionUuid(section.uuid().toString())
        .knowledgePercentage(Math.round(knowledgePercentage * 100.0) / 100.0)
        .build();
  }

  private List<MlpDto.Subject> buildAcademicsDataResponse(String boardSlug, String orgSlug) {
    List<DpsAcademicsData> dpsAcademicsData =
        dpsAcademicsRepository.getDpsAcademicsData(orgSlug, boardSlug);
    if (dpsAcademicsData.isEmpty()) {
      return Collections.emptyList();
    }

    List<SubjectsMetaData> dpsSubjectsList =
        dpsSubjectsMetaDataRepository.findByOrgSlugAndCategoryEnumAndSubjectsTypeEnum(
            orgSlug,
            SubjectsCategoryEnum.SCHOLASTIC.name(),
            SubjectsTypeEnum.MANDATORY.name(),
            boardSlug);
    Set<String> subjectSlugs =
        dpsSubjectsList.stream()
            .map(SubjectsMetaData::getWexlSubjectSlug)
            .collect(Collectors.toSet());

    List<Grade> grades = getGradesByBoard(orgSlug, boardSlug);
    Map<String, List<DpsAcademicsData>> groupedBySubject =
        dpsAcademicsData.stream()
            .filter(data -> data.getSubjectSlug() != null)
            .collect(Collectors.groupingBy(DpsAcademicsData::getSubjectSlug));

    Map<String, String> gradeMap =
        grades.stream().collect(Collectors.toMap(Grade::getSlug, Grade::getName));

    return subjectSlugs.stream()
        .map(
            subjectSlug ->
                createSubjectWithGradesResponse(
                    subjectSlug, groupedBySubject, grades, gradeMap, dpsSubjectsList))
        .toList();
  }

  private MlpDto.Subject createSubjectWithGradesResponse(
      String subjectSlug,
      Map<String, List<DpsAcademicsData>> groupedBySubject,
      List<Grade> grades,
      Map<String, String> gradeMap,
      List<SubjectsMetaData> dpsSubjectsList) {
    Optional<SubjectsMetaData> subjectMetaData =
        dpsSubjectsList.stream()
            .filter(x -> x.getWexlSubjectSlug().equals(subjectSlug))
            .findFirst();

    List<MlpDto.Grades> gradeList =
        grades.stream()
            .map(grade -> createGradeResponse(grade, groupedBySubject, subjectSlug, gradeMap))
            .toList();

    return MlpDto.Subject.builder()
        .name(subjectMetaData.map(SubjectsMetaData::getName).orElse(UNKNOWN))
        .slug(subjectSlug)
        .grades(gradeList)
        .build();
  }

  private MlpDto.Grades createGradeResponse(
      Grade grade,
      Map<String, List<DpsAcademicsData>> groupedBySubject,
      String subjectSlug,
      Map<String, String> gradeMap) {
    double knowledgePercentage =
        groupedBySubject.getOrDefault(subjectSlug, Collections.emptyList()).stream()
            .filter(
                data ->
                    data.getGradeSlug().equals(grade.getSlug())
                        && data.getKnowledgePercentage() != null)
            .mapToDouble(DpsAcademicsData::getKnowledgePercentage)
            .findFirst()
            .orElse(0.0);

    return MlpDto.Grades.builder()
        .gradeName(gradeMap.getOrDefault(grade.getSlug(), UNKNOWN))
        .gradeSlug(grade.getSlug())
        .knowledgePercentage(Math.round(knowledgePercentage * 100.0) / 100.0)
        .build();
  }

  private List<Grade> getGradesByBoard(String orgSlug, String boardSlug) {
    List<EduBoard> boardsHierarchy = curriculumService.getBoardsHierarchy(orgSlug);
    EduBoard boardData =
        boardsHierarchy.stream()
            .filter(x -> x.getSlug().equals(boardSlug))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.EduboardFind.Organization",
                        new String[] {boardSlug}));

    return sortGrades(boardData.getGrades());
  }

  private List<Grade> sortGrades(List<Grade> grades) {
    return grades.stream()
        .sorted(
            Comparator.comparingInt(grade -> GRADES_ORDER.indexOf(grade.getName().toLowerCase())))
        .toList();
  }

  private List<MlpDto.Subject> getManagerAcademicsData(String boardSlug, String gradeSlug) {
    if (gradeSlug == null) {
      return buildManagerAcademicsDataResponse(boardSlug);
    }
    return buildManagerAcademicsByGradeResponse(gradeSlug, boardSlug);
  }

  private List<MlpDto.Subject> buildManagerAcademicsByGradeResponse(
      String gradeSlug, String boardSlug) {
    List<MlpDto.Subject> respone = new ArrayList<>();
    var managerOrgs = getManagerOrgs(boardSlug);
    var orgSlugs = managerOrgs.stream().map(Organization::getSlug).distinct().toList();
    List<DpsAcademicsData> academicsData =
        dpsAcademicsRepository.getDpsManagerAcademicsByGrade(orgSlugs, boardSlug, gradeSlug);
    respone.add(
        MlpDto.Subject.builder()
            .orgResponses(buildOrgResponse(academicsData, managerOrgs, null))
            .build());
    return (respone);
  }

  private List<MlpDto.Subject> buildManagerAcademicsDataResponse(String boardSlug) {
    List<MlpDto.Subject> responeList = new ArrayList<>();
    var managerOrgs = getManagerOrgs(boardSlug);
    var orgSlugs = managerOrgs.stream().map(Organization::getSlug).distinct().toList();
    List<DpsAcademicsData> academicsData =
        dpsAcademicsRepository.getDpsManagerAcademics(orgSlugs, boardSlug);
    var subjects = getSubjects(orgSlugs, boardSlug);

    subjects.forEach(
        subject ->
            responeList.add(
                MlpDto.Subject.builder()
                    .name(subject.getName())
                    .slug(subject.getWexlSubjectSlug())
                    .orgResponses(
                        buildOrgResponse(academicsData, managerOrgs, subject.getWexlSubjectSlug()))
                    .build()));
    return responeList.stream().distinct().toList();
  }

  private List<MlpDto.OrgResponse> buildOrgResponse(
      List<DpsAcademicsData> mlpData, List<Organization> teacherOrgs, String subjectSlug) {
    List<MlpDto.OrgResponse> orgResponseList = new ArrayList<>();

    teacherOrgs.forEach(
        org -> {
          Optional<DpsAcademicsData> orgMlp =
              subjectSlug != null
                  ? mlpData.stream()
                      .filter(
                          x ->
                              x.getSubjectSlug().equals(subjectSlug)
                                  && x.getOrgSlug().equals(org.getSlug()))
                      .findAny()
                  : mlpData.stream().filter(x -> x.getOrgSlug().equals(org.getSlug())).findAny();

          if (orgMlp.isPresent()) {
            orgResponseList.add(
                MlpDto.OrgResponse.builder()
                    .orgSlug(org.getSlug())
                    .orgName(org.getName())
                    .knowledgePercentage(
                        orgMlp
                            .map(
                                dpsMlpData -> {
                                  Double knowledgePercentage = dpsMlpData.getKnowledgePercentage();
                                  return knowledgePercentage != null
                                      ? roundToTwoDecimalPlaces(knowledgePercentage)
                                      : 0.0;
                                })
                            .orElse(0.0))
                    .build());
          }
        });

    return orgResponseList;
  }

  private List<SubjectsMetaData> getSubjects(List<String> managerOrgs, String boardSlug) {
    List<SubjectsMetaData> subjectsMetaDataList = new ArrayList<>();
    managerOrgs.forEach(
        orgSlug ->
            subjectsMetaDataList.addAll(
                dpsSubjectsMetaDataRepository.findByOrgSlugAndCategoryEnumAndSubjectsTypeEnum(
                    orgSlug,
                    SubjectsCategoryEnum.SCHOLASTIC.name(),
                    SubjectsTypeEnum.MANDATORY.name(),
                    boardSlug)));

    List<SubjectsMetaData> list = new ArrayList<>();
    Set<SubjectsMetaData> uniqueValues = new HashSet<>();
    for (SubjectsMetaData subject : subjectsMetaDataList) {
      subject.setName(subject.getName().trim());
      if (uniqueValues.add(subject)) {
        list.add(subject);
      }
    }
    return list;
  }

  private List<Organization> getManagerOrgs(String boardSlug) {
    User user = authService.getUserDetails();
    return teacherOrgsService.getChildOrgs(user.getAuthUserId()).stream()
        .filter(
            org ->
                org.getCurriculum().getBoards().stream()
                    .anyMatch(board -> board.getSlug().equals(boardSlug)))
        .distinct()
        .toList();
  }

  private double roundToTwoDecimalPlaces(double value) {
    return Math.round(value * 100.0) / 100.0;
  }
}
