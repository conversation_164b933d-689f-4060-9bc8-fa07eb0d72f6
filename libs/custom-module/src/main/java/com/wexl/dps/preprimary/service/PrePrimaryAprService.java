package com.wexl.dps.preprimary.service;

import com.wexl.dps.dto.LearningLevel;
import com.wexl.dps.preprimary.model.AcademicPerformanceRecord;
import com.wexl.dps.preprimary.model.DpsPrePrimaryAprDto;
import com.wexl.dps.preprimary.repository.DpsPreprimaryAprRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.model.Student;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PrePrimaryAprService {
  private final StudentRepository studentRepository;
  private final UserRepository userRepository;
  private final DpsPreprimaryAprRepository dpsPreprimaryAprRepository;
  private final TermRepository termRepository;
  private final StudentService studentService;
  private final TeacherRepository teacherRepository;
  private final SectionRepository sectionRepository;
  private final ContentService contentService;
  private final String DPS_AEROCITY_ORGSLUG = "del189476";
  private final String NURSERY_GRADE_SLUG = "nur";

  public void createStudentApr(String studentAuthId, DpsPrePrimaryAprDto.SubjectRequest request) {
    buildAprRequest(studentAuthId, request);
  }

  private void buildAprRequest(String studentAuthId, DpsPrePrimaryAprDto.SubjectRequest request) {
    try {
      var user = userRepository.getUserByAuthUserId(studentAuthId);
      var student =
          studentRepository.getStudentByAuthUserIdAndOrgSlug(studentAuthId, user.getOrganization());
      var section = student.getSection();
      Term term = validateTermId(request.termId());
      var termAssessment = term.getTermAssessments().get(0);
      String teacherName = getTeacherName(section);
      AcademicPerformanceRecord aprRecord =
          dpsPreprimaryAprRepository.findByStudentIdAndTermId(student.getId(), request.termId());
      if (aprRecord == null) {
        aprRecord = createAprRecord(student, section, teacherName, termAssessment, request);
        dpsPreprimaryAprRepository.save(aprRecord);
      } else {
        updateAprRecord(aprRecord, section, teacherName, termAssessment, request);
      }
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private void updateAprRecord(
      AcademicPerformanceRecord aprRecord,
      Section section,
      String teacherName,
      TermAssessment termAssessment,
      DpsPrePrimaryAprDto.SubjectRequest request) {
    aprRecord.setTeacherId(
        section.getClassTeacher() == null ? null : section.getClassTeacher().getId());
    aprRecord.setTeacherName(teacherName);
    aprRecord.setGradeSlug(section.getGradeSlug());
    aprRecord.setTermAssessments(termAssessment.getName());
    aprRecord.setMath(request.mathematics());
    aprRecord.setSpeaking(request.speaking());
    aprRecord.setReading(request.reading());
    aprRecord.setWriting(request.writing());
    aprRecord.setListening(request.listening());
    aprRecord.setPhyDev(request.phyDev());
    aprRecord.setPsew(request.psew());
    aprRecord.setUntw(request.untw());
    aprRecord.setTeluguOrHindi(request.teluguOrHindi());
    dpsPreprimaryAprRepository.save(aprRecord);
  }

  private AcademicPerformanceRecord createAprRecord(
      Student student,
      Section section,
      String teacherName,
      TermAssessment termAssessment,
      DpsPrePrimaryAprDto.SubjectRequest request) {
    return AcademicPerformanceRecord.builder()
        .studentId(student.getId())
        .teacherId(section.getClassTeacher() == null ? null : section.getClassTeacher().getId())
        .termId(request.termId())
        .teacherName(teacherName)
        .gradeSlug(section.getGradeSlug())
        .termAssessments(termAssessment.getName())
        .math(request.mathematics())
        .speaking(request.speaking())
        .reading(request.reading())
        .writing(request.writing())
        .listening(request.listening())
        .phyDev(request.phyDev())
        .psew(request.psew())
        .untw(request.untw())
        .teluguOrHindi(request.teluguOrHindi())
        .build();
  }

  private String getTeacherName(Section section) {
    if (section.getClassTeacher() == null) {
      return "-";
    }

    var teacher = teacherRepository.findById(section.getClassTeacher().getId());
    if (teacher.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.TeacherNotFound");
    }
    var teacherValue = teacher.get();
    var userInfo = teacherValue.getUserInfo();
    return userInfo.getFirstName() + " " + userInfo.getLastName();
  }

  public void createAttendance(String studentAuthId, DpsPrePrimaryAprDto.Attendance request) {
    var user = userRepository.getUserByAuthUserId(studentAuthId);
    var student =
        studentRepository.getStudentByAuthUserIdAndOrgSlug(studentAuthId, user.getOrganization());
    Term term = validateTermId(request.termId());
    var termAssessment = term.getTermAssessments().get(0);
    AcademicPerformanceRecord aprRecord =
        dpsPreprimaryAprRepository.findByStudentIdAndTermId(student.getId(), request.termId());
    if (aprRecord == null) {
      aprRecord = new AcademicPerformanceRecord();
      aprRecord.setStudentId(student.getId());
      aprRecord.setTermId(request.termId());
      aprRecord.setTermAssessments(termAssessment.getName());
      aprRecord.setAttendancePresent(request.attendancePresent());
      aprRecord.setAttendanceTotal(request.totalAttendance());
      aprRecord.setGradeSlug(student.getSection().getGradeSlug());
      dpsPreprimaryAprRepository.save(aprRecord);
    } else {
      aprRecord.setAttendancePresent(request.attendancePresent());
      aprRecord.setAttendanceTotal(request.totalAttendance());
      dpsPreprimaryAprRepository.save(aprRecord);
    }
  }

  public void postRemarks(String studentAuthId, DpsPrePrimaryAprDto.Remarks request) {
    var user = userRepository.getUserByAuthUserId(studentAuthId);
    var student =
        studentRepository.getStudentByAuthUserIdAndOrgSlug(studentAuthId, user.getOrganization());
    Term term = validateTermId(request.termId());
    var termAssessment = term.getTermAssessments().get(0);
    AcademicPerformanceRecord aprRecord =
        dpsPreprimaryAprRepository.findByStudentIdAndTermId(student.getId(), request.termId());
    if (aprRecord == null) {
      aprRecord = new AcademicPerformanceRecord();
      aprRecord.setStudentId(student.getId());
      aprRecord.setTermId(request.termId());
      aprRecord.setTermAssessments(termAssessment.getName());
      aprRecord.setRemarks(request.remarks());
      aprRecord.setGradeSlug(student.getSection().getGradeSlug());
      dpsPreprimaryAprRepository.save(aprRecord);
    } else {
      aprRecord.setRemarks(request.remarks());
      dpsPreprimaryAprRepository.save(aprRecord);
    }
  }

  public DpsPrePrimaryAprDto.Summary getStudentReport(Long aprId) {
    AcademicPerformanceRecord performanceRecord = validateAprId(aprId);
    return buildPrimaryReportCard(Collections.singletonList(performanceRecord));
  }

  public DpsPrePrimaryAprDto.Summary buildPrimaryReportCard(
      List<AcademicPerformanceRecord> performanceRecord) {
    if (performanceRecord == null || performanceRecord.isEmpty()) {
      return null;
    }
    var apr = performanceRecord.getFirst();
    var attendanceTotal =
        performanceRecord.stream()
            .map(AcademicPerformanceRecord::getAttendanceTotal)
            .filter(Objects::nonNull)
            .toList();
    var gradeName = contentService.getGradeNameBySlug(apr.getGradeSlug());
    return DpsPrePrimaryAprDto.Summary.builder()
        .teacherId(apr.getTeacherId() == null ? null : apr.getTeacherId())
        .teacherName(apr.getTeacherName() == null ? null : apr.getTeacherName())
        .assessmentName(apr.getTermAssessments())
        .totalAttendance(attendanceTotal.isEmpty() ? 0L : attendanceTotal.getFirst())
        .gradeSlug(apr.getGradeSlug())
        .gradeName(gradeName)
        .termId(apr.getTermId())
        .studentDetails(studentDetails(performanceRecord))
        .build();
  }

  public List<DpsPrePrimaryAprDto.StudentDetails> studentDetails(
      List<AcademicPerformanceRecord> performanceRecord) {
    List<DpsPrePrimaryAprDto.StudentDetails> response = new ArrayList<>();
    for (AcademicPerformanceRecord record : performanceRecord) {
      var student = studentService.findStudentById(record.getStudentId());
      var studentName =
          student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();
      response.add(
          DpsPrePrimaryAprDto.StudentDetails.builder()
              .aprId(record.getId())
              .untw(record.getUntw())
              .psew(record.getPsew())
              .writing(record.getWriting())
              .listening(record.getListening())
              .reading(record.getReading())
              .speaking(record.getSpeaking())
              .mathematics(record.getMath())
              .phyDev(record.getPhyDev())
              .teluguOrHindi(record.getTeluguOrHindi())
              .studentName(studentName)
              .studentId(student.getId())
              .remarks(record.getRemarks())
              .attendancePresent(record.getAttendancePresent())
              .build());
    }
    return response;
  }

  public Term validateTermId(Long termId) {
    return termRepository
        .findById(termId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidTerm"));
  }

  private AcademicPerformanceRecord validateAprId(Long aprId) {
    return dpsPreprimaryAprRepository
        .findById(aprId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.AprIdNotFound"));
  }

  public DpsPrePrimaryAprDto.Summary getTermStudents(Long termId, String sectionUuid) {
    var filterSectionAprRecords = getStudentsByTermAndSection(termId, sectionUuid);
    return buildPrimaryReportCard(filterSectionAprRecords);
  }

  private List<AcademicPerformanceRecord> getStudentsByTermAndSection(
      Long termId, String sectionUuid) {
    var aprRecords = dpsPreprimaryAprRepository.findAllByTermId(termId);
    var section =
        sectionRepository
            .findByUuid(UUID.fromString(sectionUuid))
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionNotFound"));
    var sectionStudentIds =
        studentRepository.getStudentsBySection(section).stream().map(Student::getId).toList();
    return aprRecords.stream()
        .filter(apr -> sectionStudentIds.contains(apr.getStudentId()))
        .toList();
  }

  public void updateSkillEntry(Long aprId, DpsPrePrimaryAprDto.SubjectRequest request) {
    AcademicPerformanceRecord performanceRecord = validateAprId(aprId);
    performanceRecord.setReading(request.reading());
    performanceRecord.setSpeaking(request.speaking());
    performanceRecord.setWriting(request.writing());
    performanceRecord.setListening(request.listening());
    performanceRecord.setPsew(request.psew());
    performanceRecord.setUntw(request.untw());
    performanceRecord.setMath(request.mathematics());
    performanceRecord.setPhyDev(request.phyDev());
    performanceRecord.setTeluguOrHindi(request.teluguOrHindi());
    dpsPreprimaryAprRepository.save(performanceRecord);
  }

  public void updateRemarks(Long aprId, DpsPrePrimaryAprDto.Remarks request) {
    AcademicPerformanceRecord performanceRecord = validateAprId(aprId);
    performanceRecord.setRemarks(request.remarks());
    dpsPreprimaryAprRepository.save(performanceRecord);
  }

  public void updateAttendance(Long aprId, DpsPrePrimaryAprDto.Attendance request) {
    AcademicPerformanceRecord performanceRecord = validateAprId(aprId);
    performanceRecord.setAttendancePresent(request.attendancePresent());
    dpsPreprimaryAprRepository.save(performanceRecord);

    List<AcademicPerformanceRecord> records = new ArrayList<>();
    var student =
        studentRepository
            .findById(performanceRecord.getStudentId())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound"));
    var sectionStudentAprRecords =
        getStudentsByTermAndSection(request.termId(), student.getSection().getUuid().toString());
    for (var studentAprRecord : sectionStudentAprRecords) {
      studentAprRecord.setAttendanceTotal(request.totalAttendance());
      records.add(studentAprRecord);
    }
    dpsPreprimaryAprRepository.saveAll(records);
  }

  public ReportCardConfigDto.FirstPageFirstTable getStudentPrePrimary(
      Student student, Long termId, String orgSlug) {

    var academicPerformanceRecord =
        dpsPreprimaryAprRepository.findByStudentIdAndTermId(student.getId(), termId);
    var apr =
        Objects.nonNull(academicPerformanceRecord)
            ? academicPerformanceRecord
            : new AcademicPerformanceRecord();

    ReportCardConfigDto.FirstPageFirstTable.FirstPageFirstTableBuilder firstTable =
        ReportCardConfigDto.FirstPageFirstTable.builder()
            .title("ACADEMIC PERFORMANCE RECORD")
            .listeningGrade(getGradeByLearningLevel(apr.getListening()))
            .listeningValue(formatLearningLevel(getLearningLevel(apr.getListening())))
            .speakingGrade(getGradeByLearningLevel(apr.getSpeaking()))
            .speakingValue(formatLearningLevel(getLearningLevel(apr.getSpeaking())))
            .readingGrade(getGradeByLearningLevel(apr.getReading()))
            .readingValue(formatLearningLevel(getLearningLevel(apr.getReading())))
            .writingGrade(getGradeByLearningLevel(apr.getWriting()))
            .writingValue(formatLearningLevel(getLearningLevel(apr.getWriting())))
            .psewGrade(getGradeByLearningLevel(apr.getPsew()))
            .psewValue(formatLearningLevel(getLearningLevel(apr.getPsew())))
            .phyDevGrade(getGradeByLearningLevel(apr.getPhyDev()))
            .phyDevValue(formatLearningLevel(getLearningLevel(apr.getPhyDev())))
            .mathGrade(getGradeByLearningLevel(apr.getMath()))
            .mathValue(formatLearningLevel(getLearningLevel(apr.getMath())))
            .untwGrade(getGradeByLearningLevel(apr.getUntw()))
            .untwValue(formatLearningLevel(getLearningLevel(apr.getUntw())))
            .comment(apr.getRemarks());
    if (orgSlug.equals(DPS_AEROCITY_ORGSLUG)
        && !Objects.equals(student.getSection().getGradeSlug(), NURSERY_GRADE_SLUG)) {
      firstTable.teluguOrHindiGrade(getGradeByLearningLevel(apr.getTeluguOrHindi()));
      firstTable.teluguOrHindiValue(formatLearningLevel(getLearningLevel(apr.getTeluguOrHindi())));
    }
    return firstTable.build();
  }

  private String formatLearningLevel(String learningLevel) {
    return learningLevel.replace("_", "   ");
  }

  private String getLearningLevel(LearningLevel learningLevel) {
    return Objects.nonNull(learningLevel) ? learningLevel.name() : StringUtils.EMPTY;
  }

  public String getGradeByLearningLevel(LearningLevel learningLevel) {
    try {
      return switch (learningLevel) {
        case PROFICIENT_LEARNER -> "A+";
        case SKILLFUL_LEARNER -> "A*";
        case CONFIDENT_LEARNER -> "A";
        case EVOLVING_LEARNER -> "D";
        case PROMINENT_LEARNER -> "B";
        case PROGRESSING_LEARNER -> "C";
        case ABSENT, ABSENT_ABS -> "ABS";
        case N_A -> "N/A";
        case MEDICAL_LEAVE -> "ML";
        case PERMITTED_ABSENT -> "PA";
        case A -> "A";
        case B -> "B";
        case C -> "C";
        case D -> "D";
        case NA -> "NA";
        case A_STAR -> "A*";
        case ALWAYS, OFTEN, OCCASIONALLY, RARELY, NEVER -> null;
      };
    } catch (Exception e) {
      return StringUtils.EMPTY;
    }
  }

  public String getAttendance(Student student, Long termId) {
    var academicPerformanceRecord =
        dpsPreprimaryAprRepository.findByStudentIdAndTermId(student.getId(), termId);
    return Objects.nonNull(academicPerformanceRecord)
            && Objects.nonNull(academicPerformanceRecord.getAttendancePresent())
        ? academicPerformanceRecord.getAttendancePresent()
            + " / "
            + academicPerformanceRecord.getAttendanceTotal()
        : "NA";
  }
}
