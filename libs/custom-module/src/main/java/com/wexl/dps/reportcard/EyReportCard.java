package com.wexl.dps.reportcard;

import com.wexl.dps.assesmentobjectives.service.AssessmentObjectiveService;
import com.wexl.dps.learningmilestones.service.LmrStudentService;
import com.wexl.dps.preprimary.service.PrePrimaryAprService;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.repository.TermRepository;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EyReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;

  private final PrePrimaryAprService prePrimaryAprService;

  private final StudentService studentService;

  private final TermRepository termRepository;

  private final UserService userService;

  private final LmrStudentService lmrStudentService;

  private final AssessmentObjectiveService assessmentObjectiveService;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request.termId(), org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("ey-report-card.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  private ReportCardConfigDto.Body buildBody(User user, Long termId, Organization organization) {
    var student = studentService.findByUserInfo(user);
    var classTeacher = student.getSection().getClassTeacher();
    String gradeFacilitatorName =
        (classTeacher != null && classTeacher.getUserInfo() != null)
            ? userService.getNameByUserInfo(classTeacher.getUserInfo())
            : "-";

    String gradeName = student.getSection().getName();
    if (gradeName != null && gradeName.length() > 2) {
      gradeName = new StringBuilder(gradeName).insert(gradeName.length() - 1, "-").toString();
    }

    return ReportCardConfigDto.Body.builder()
        .schoolSectionName(student.getSection().getName())
        .grade(gradeName)
        .orgSlug(organization.getSlug())
        .gradeSlug(student.getSection().getGradeSlug())
        .gradeFacilitatorName(gradeFacilitatorName)
        .termSlug(String.valueOf(termId))
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .attendance(prePrimaryAprService.getAttendance(student, termId))
        .assessment(getAssessmentName(termId))
        .firstTable(
            prePrimaryAprService.getStudentPrePrimary(student, termId, organization.getSlug()))
        .secondPage(lmrStudentService.getEyLmrReport(student, termId, organization.getSlug()))
        .aoReport(
            assessmentObjectiveService.getEyAssessmentReports(
                student, termId, organization.getSlug()))
        .build();
  }

  public String getAssessmentName(Long termId) {

    return termRepository
        .findById(termId)
        .map(
            term -> {
              if (term.getSlug().contains("t1")) {
                return "MID TERM ASSESSMENT";
              } else if (term.getSlug().contains("t2")) {
                return "END TERM ASSESSMENT";
              } else {
                return term.getName();
              }
            })
        .orElse(StringUtils.EMPTY);
  }
}
