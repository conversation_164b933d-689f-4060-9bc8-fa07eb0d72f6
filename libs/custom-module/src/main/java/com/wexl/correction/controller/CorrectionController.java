package com.wexl.correction.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;
import static java.time.ZoneOffset.UTC;

import com.wexl.correction.dto.SubjectiveCorrectionDto.SubjectiveCorrectionSubmitAnswerRequest;
import com.wexl.correction.dto.SubjectiveCorrectionDto.SubjectiveCorrectionSubmitAnswerResponse;
import com.wexl.correction.service.CorrectionService;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class CorrectionController {

  private final CorrectionService correctionService;

  @PostMapping("/orgs/{orgSlug}/test-schedules/{testScheduleId}/pdf-upload")
  @IsTeacher
  public Map<String, String> getPdfUploadPreSignedUrl(
      @PathVariable String orgSlug, @PathVariable Long testScheduleId) {
    String reference =
        DateTimeFormatter.ofPattern("yyyyMMddhhmmssSSS").format(LocalDateTime.now(UTC));
    return Map.of(
        "reference",
        reference,
        "url",
        correctionService.getPdfFileUploadUrl(orgSlug, reference, testScheduleId));
  }

  @IsStudent
  @PostMapping("/orgs/{orgSlug}/test-schedules/{testScheduleId}/subjective-corrections")
  public SubjectiveCorrectionSubmitAnswerResponse processSubjectiveCorrection(
      @PathVariable Long testScheduleId,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken,
      @RequestBody SubjectiveCorrectionSubmitAnswerRequest correctionRequest) {
    return correctionService.processSubjectiveCorrection(
        testScheduleId, correctionRequest, bearerToken);
  }
}
