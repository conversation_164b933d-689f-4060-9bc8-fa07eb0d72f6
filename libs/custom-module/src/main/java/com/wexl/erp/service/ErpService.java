package com.wexl.erp.service;

import com.wexl.dps.model.ErpIntegration;
import com.wexl.dps.repository.ErpIntegrationRepository;
import com.wexl.erp.dto.ErpDto;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ErpService {

  private final ErpTeacherProcessor erpTeacherProcessor;
  private final ErpStudentProcessor erpStudentProcessor;
  private final ErpIntegrationRepository erpIntegrationRepository;

  public void syncErpProcess(ErpDto.ErpEntityChangeResponse response) {
    log.info("Processing response: {}", response);
    response
        .erpEntityChanges()
        .forEach(
            request -> {
              if ("teacher".equals(request.type())) {
                erpTeacherProcessor.process(request);
              } else if ("student".equals(request.type())) {
                erpStudentProcessor.process(request);
              }
            });

    erpIntegrationRepository.save(
        ErpIntegration.builder()
            .type(response.erpEntityChanges().getFirst().type())
            .lastSyncedAt(LocalDateTime.now())
            .json(String.valueOf(response))
            .build());
  }
}
