package com.wexl.retail.subjects.repository;

import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.service.StudentSubjectMetadata;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SubjectsMetaDataRepository extends JpaRepository<SubjectsMetaData, Long> {

  @Query(
      value =
          """
                             select smd.* from subject_metadata smd
                             where (cast((:type) as varChar) is null or smd.type in (:type))
                             and (cast((:boardSlug) as varChar) is null or smd.board_slug in (:boardSlug))
                            and (cast((:gradeSlug) as varChar) is null or smd.grade_slug in (:gradeSlug))
                            and (cast((:category) as varChar) is null or smd.category in (:category))
                            and org_slug = :orgSlug
                            order by created_at desc
                                                           """,
      nativeQuery = true)
  List<SubjectsMetaData> getSubjects(
      String boardSlug, String gradeSlug, String category, String type, String orgSlug);

  List<SubjectsMetaData> findByGradeSlugAndBoardSlug(String grade, String board);

  SubjectsMetaData
      findByOrgSlugAndWexlSubjectSlugAndNameAndGradeSlugAndBoardSlugAndStatusAndDeletedAtIsNull(
          String orgSlug,
          String wexlSubjectSlug,
          String name,
          String gradeSlug,
          String boardSlug,
          Boolean status);

  Optional<SubjectsMetaData> findByOrgSlugAndNameAndGradeSlugAndBoardSlug(
      String orgSlug, String subject, String grade, String board);

  List<SubjectsMetaData> findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
      String orgSlug, List<String> subjectSlugs, String gradeSlug, String boardSlug);

  List<SubjectsMetaData> findByOrgSlugAndGradeSlugAndBoardSlug(
      String orgSlug, String gradeSlug, String boardSlug);

  Optional<SubjectsMetaData> findByIdInAndOrgSlug(List<Long> ids, String orgSlug);

  @Query(
      value =
          """
          select concat(u.first_name, u.last_name) as studentName, s2.grade_name as gradeName, s2."name" as section, sm."name" as subject, sm.id as subjectMetadataId, s.class_roll_number as classRollNumber from subject_metadata sm
          join subject_metadata_students sms on sm.id = sms.subject_metadata_id
          join students s on sms.student_id = s.id
          join sections s2 on s2.id = s.section_id
          join users u on u.id = s.user_id
          where sm.org_slug = :orgSlug and sm.board_slug = :boardSlug and sm.grade_slug = :gradeSlug
          """,
      nativeQuery = true)
  List<StudentSubjectMetadata> getStudentSubjectMetadata(
      String orgSlug, String boardSlug, String gradeSlug);
}
