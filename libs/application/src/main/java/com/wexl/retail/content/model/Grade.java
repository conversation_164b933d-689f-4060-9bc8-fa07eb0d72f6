package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class Grade {

  private int id;

  @JsonProperty("name")
  private String name;

  @JsonProperty("status")
  private String status;

  private String slug;

  @JsonProperty("order")
  private Integer order;
}
