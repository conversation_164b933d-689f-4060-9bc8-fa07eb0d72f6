package com.wexl.retail.student.answer;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.RandomOptionGenerator;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.MultipleChoiceQuestionWithAnswer;
import com.wexl.retail.content.model.Question;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.liveworksheet.service.LiveWorkSheetService;
import com.wexl.retail.notification.NotificationService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamActivityService;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.student.exam.SectionWiseData;
import com.wexl.retail.student.exam.publisher.ExamCompletionEventPublisher;
import com.wexl.retail.student.exam.revision.domain.ExamRevisionRequest;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.UploadService;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Data
public class StudentAnswerService {

  public final ExamService examService;
  private final StudentAnswerRepository studentAnswerRepository;
  private final StudentAnswerTransformer studentAnswerTransformer;
  private final StrapiService strapiService;
  private final TestDefinitionService testDefinitionService;
  private final LiveWorkSheetService liveWorkSheetService;
  @Autowired EmailService emailService;
  @Autowired AuthService authService;
  @Autowired NotificationService notificationService;
  @Autowired ExamActivityService examActivityService;
  @Autowired UploadService uploadService;
  @Autowired StorageService storageService;
  @Autowired StudentRepository studentRepository;
  @Autowired private ContentService contentService;
  @Autowired private ExamRevisionEventPublisher examRevisionEventPublisher;
  @Autowired private StudentService studentService;
  @Autowired private ExamCompletionEventPublisher examCompletionEventPublisher;
  @Autowired private RandomOptionGenerator randomOptionGenerator;

  @Value("${app.contentToken}")
  private String bearerToken;

  public StudentAnswerService(
      StudentAnswerRepository studentAnswerRepository,
      @Lazy StudentAnswerTransformer studentAnswerTransformer,
      @Lazy ExamService examService,
      StrapiService strapiService,
      TestDefinitionService testDefinitionService,
      ExamCompletionEventPublisher examCompletionEventPublisher,
      LiveWorkSheetService liveWorkSheetService,
      RandomOptionGenerator randomOptionGenerator) {
    this.studentAnswerRepository = studentAnswerRepository;
    this.studentAnswerTransformer = studentAnswerTransformer;
    this.examService = examService;
    this.strapiService = strapiService;
    this.testDefinitionService = testDefinitionService;
    this.examCompletionEventPublisher = examCompletionEventPublisher;
    this.liveWorkSheetService = liveWorkSheetService;
    this.randomOptionGenerator = randomOptionGenerator;
  }

  protected List<StudentAnswerResponse> getExamAnswers(int page, int size, Sort sort) {
    return studentAnswerRepository.findAllStudents(PageRequest.of(page, size, sort)).stream()
        .map(studentAnswerTransformer::mapStudentAnswerToAnswerResponse)
        .toList();
  }

  protected StudentAnswerResponse findById(long id) {
    log.debug("Find By Id called on : " + id);
    return studentAnswerRepository
        .findById(id)
        .map(studentAnswerTransformer::mapStudentAnswerToAnswerResponse)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidAnswerId"));
  }

  public StudentAnswerResponse submitExamAnswers(
      StudentAnswerRequest studentAnswerRequest, String bearerToken) {
    var exam = examService.findById(studentAnswerRequest.getExamId());
    var examStats = getExamStats(studentAnswerRequest, bearerToken, exam);

    var studentAnswerResponse =
        StudentAnswerResponse.builder().examQuestion(examStats.getExamQuestions()).build();
    if (!examStats.getExamQuestions().isEmpty()) {
      exam.setNoOfQuestions(examStats.getNoOfQuestions());
      exam.setCorrectAnswers(examStats.getCorrectAnswers());
      exam.setMarksScored(examStats.getMarksScored());
      exam.setNegativeMarksScored(0f);
      exam.setTotalMarks(examStats.getTotalMarks());
      exam.setCompleted(true);
      exam.setCorrected(isHavingSubjectiveQuestions(studentAnswerRequest.getExamQuestions()));
      if (exam.getTestDefinition() != null
          && !exam.getTestDefinition().getTestDefinitionSections().isEmpty()) {
        exam.setExamAttributes(
            buildExamAttributes(examStats, exam.getTestDefinition().getTestDefinitionSections()));
      }
      if (Objects.isNull(exam.getEndTime())) {
        exam.setEndTime(Timestamp.from(Instant.now()));
        examService.save(exam);
        examActivityService.notifyTestCompletion(exam, testDefinitionService);
      }
      examCompletionEventPublisher.publishExamCompletion(exam);

      studentAnswerResponse.setExamDetails(buildExamDetails(exam, examStats.getPercentage()));
    }

    return studentAnswerResponse;
  }

  private SectionWiseData.ExamAttributes buildExamAttributes(
      ExamStats examStats, List<TestDefinitionSection> testDefinitionSections) {
    return SectionWiseData.ExamAttributes.builder()
        .sectionWiseTestData(buildSectionWiseData(examStats, testDefinitionSections))
        .build();
  }

  private List<SectionWiseData.SectionWiseTestData> buildSectionWiseData(
      ExamStats examStats, List<TestDefinitionSection> testDefinitionSections) {
    List<SectionWiseData.SectionWiseTestData> sectionWiseData = new ArrayList<>();
    sectionWiseData.add(
        SectionWiseData.SectionWiseTestData.builder()
            .sectionId(testDefinitionSections.getFirst().getId())
            .sectionName(testDefinitionSections.getFirst().getName())
            .marksScored(examStats.getMarksScored())
            .totalMarks(examStats.getTotalMarks())
            .noOfQuestions((long) examStats.getNoOfQuestions())
            .build());
    return sectionWiseData;
  }

  private boolean isHavingSubjectiveQuestions(List<ExamQuestion> examQuestions) {
    return examQuestions.stream()
        .noneMatch(
            question -> QuestionType.SUBJECTIVE.getType().equalsIgnoreCase(question.getType()));
  }

  private ExamStats getExamStats(
      StudentAnswerRequest studentAnswerRequest, String bearerToken, Exam exam) {
    log.debug("Add New Answer service called");
    var examStats = new ExamStats();
    var examQuestions = new ArrayList<ExamQuestion>();
    var testDefQuestions = examService.getTestDefQuestions(exam);
    var user = authService.getUserDetails();

    for (ExamAnswer examAnswer :
        studentAnswerTransformer.mapAnswerRequestToStudentAnswer(
            studentAnswerRequest, bearerToken)) {
      QuestionDto.SearchQuestionResponse questionResponse;
      final var questionType = QuestionType.getByType(examAnswer.getType());
      if (Constants.ASSIGNMENT_EXAM == exam.getExamType()
          || Constants.COURSE_ASSIGNMENT == exam.getExamType()) {
        questionResponse =
            buildSearchQuestionResponse(
                contentService.getAssignmentQuestionByUuid(
                    bearerToken, examAnswer.getQuestionUuid()));
      } else {
        questionResponse =
            contentService.getQuestionsByUuid(
                bearerToken,
                questionType.toString(),
                examAnswer.getQuestionUuid(),
                user.getOrganization());
        if (questionResponse.questions().isEmpty()) {
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST,
              "Question Not found with uuid  " + examAnswer.getQuestionUuid());
        }
      }
      var question = questionResponse.questions().getFirst();
      var isCorrect = isCorrect(questionType, question, examAnswer);
      examAnswer.setCorrect(isCorrect);
      var marksPerQuestion =
          examService.getAssignedMarksForTestQuestion(testDefQuestions, question);
      examAnswer.setMarksPerQuestion(marksPerQuestion);
      var marksScoredPerQuestion = getMarksScoredPerQuestion(examStats, examAnswer);
      examAnswer.setMarksScoredPerQuestion(marksScoredPerQuestion);
      examStats.setNoOfQuestions(examStats.getNoOfQuestions() + 1);

      if (Objects.isNull(exam.getEndTime())) {
        final var examAnswerSaved = studentAnswerRepository.save(examAnswer);
        examAnswerSaved
            .getExamAnswerAttachments()
            .forEach(attachment -> attachment.setAnswer(examAnswerSaved));
        examAnswer = studentAnswerRepository.save(examAnswerSaved);
      }
      examQuestions.add(
          buildExamQuestion(testDefQuestions, examAnswer, question, marksScoredPerQuestion));
      if (isHavingSubjectiveQuestions(examQuestions)) {
        examRevisionEventPublisher.publishEvent(
            ExamRevisionRequest.builder()
                .student(exam.getStudent())
                .exam(exam)
                .questionUuid(question.uuid())
                .isCorrect(isCorrect)
                .build());
      }
    }

    examStats.setExamQuestions(examQuestions);
    examStats.setPercentage(((examStats.getMarksScored() / examStats.getTotalMarks()) * 100));
    return examStats;
  }

  private boolean isCorrect(
      QuestionType questionType, QuestionDto.Question questionByUuid, ExamAnswer examAnswer) {
    if (QuestionType.MCQ.equals(questionType)) {
      return questionByUuid.mcq().answer().equals(examAnswer.getSelectedOption().longValue());
    } else if (QuestionType.LIVE_WORKSHEET.equals(questionType)) {
      return questionByUuid
          .worksheet()
          .getFirst()
          .workSheetQuestion()
          .answer()
          .equals(examAnswer.getAnswer());
    }
    return false;
  }

  public Question getContentServiceQuestionByUuid(
      String bearerToken, Exam exam, QuestionType questionType, String questionUuid) {
    if (Constants.LIVE_WORKSHEET == exam.getExamType()) {
      return liveWorkSheetService.getLiveWorksheetQuestionByUuid(
          bearerToken, exam.getTestDefinition(), questionUuid);
    }
    if (Constants.ASSIGNMENT_EXAM == exam.getExamType()
        || Constants.COURSE_ASSIGNMENT == exam.getExamType()) {
      return contentService.getAssignmentQuestionByUuid(bearerToken, questionUuid);
    }
    if (questionUuid.equals(Constants.DEFAULT_QUESTION_UUID)) {
      return contentService.getQuestionBySubjectAndUuid(
          bearerToken, questionType, Constants.DEFAULT_SUBJECT_ID, questionUuid);
    }
    return contentService.getQuestionByUuid(bearerToken, questionType, questionUuid);
  }

  private float getMarksScoredPerQuestion(ExamStats examStats, ExamAnswer examAns) {
    var marksScoredPerQuestion = (examAns.isCorrect() ? examAns.getMarksPerQuestion() : 0);
    examStats.setTotalMarks(examStats.getTotalMarks() + examAns.getMarksPerQuestion());
    if (examAns.isCorrect()) {
      examStats.setCorrectAnswers(examStats.getCorrectAnswers() + 1);
      examStats.setMarksScored(examStats.getMarksScored() + marksScoredPerQuestion);
    }
    return marksScoredPerQuestion;
  }

  private ExamQuestion buildExamQuestion(
      Map<String, TestQuestion> testDefQuestions,
      ExamAnswer examAnswer,
      QuestionDto.Question question,
      Float marksScoredPerQuestion) {
    var examQuestion =
        ExamQuestion.builder()
            .questionDetails(transformToQuestion(question))
            .questionsResponse(question)
            .isCorrect(examAnswer.isCorrect())
            .questionId(question.id())
            .questionUuid(question.uuid())
            .selectedAnswer(examAnswer.getSelectedOption())
            .answer(examAnswer.getAnswer())
            .type(question.type().toString())
            .answerType(examAnswer.getAnswerType())
            .marksScored(marksScoredPerQuestion)
            .attachments(
                examAnswer.getExamAnswerAttachments().parallelStream()
                    .map(
                        attachment ->
                            storageService.generatePreSignedUrlForFetch(attachment.getPath()))
                    .toList())
            .marks(examService.getAssignedMarksForTestQuestion(testDefQuestions, question))
            .feedback(examAnswer.getFeedback())
            .build();

    addElaborateAnswerIfMcqQuestion(question, examQuestion, examAnswer);

    return examQuestion;
  }

  public Question transformToQuestion(QuestionDto.Question questionByUuid) {
    Question question = new Question();
    if (QuestionType.MCQ.equals(questionByUuid.type())
        || QuestionType.SUBJECTIVE.equals(questionByUuid.type())) {
      question.setQuestions(questionByUuid.question());
      question.setId(questionByUuid.id());
      question.setUuid(questionByUuid.uuid());
      question.setAnswer(
          Objects.nonNull(questionByUuid.mcq())
              ? Math.toIntExact(questionByUuid.mcq().answer())
              : null);
      question.setType(questionByUuid.type().toString());
      question.setMarks(questionByUuid.marks());
      question.setActive(questionByUuid.active());
      question.setQuestionCategoryId(questionByUuid.questionCategoryId());
      question.setComplexity(questionByUuid.complexity());
      question.setOption1(
          Objects.nonNull(questionByUuid.mcq()) ? questionByUuid.mcq().option1() : null);
      question.setOption2(
          Objects.nonNull(questionByUuid.mcq()) ? questionByUuid.mcq().option2() : null);
      question.setOption3(
          Objects.nonNull(questionByUuid.mcq()) ? questionByUuid.mcq().option3() : null);
      question.setOption4(
          Objects.nonNull(questionByUuid.mcq()) ? questionByUuid.mcq().option4() : null);
      question.setChapterSlug(questionByUuid.chapterSlug());
      question.setSubtopicSlug(questionByUuid.subtopicSlug());
      question.setSubjectSlug(questionByUuid.subjectSlug());
      question.setSubjectName(
          Objects.nonNull(questionByUuid.subjectSlug())
              ? strapiService.getSubjectNameBySlug(questionByUuid.subjectSlug())
              : null);
      question.setExplanation(getExplanation(questionByUuid));
      question.setOrganizationSlug(questionByUuid.organizationSlug());
      question.setOrganization(questionByUuid.organization());
      question.setPublished(questionByUuid.published() != null && questionByUuid.published());
    } else if (QuestionType.LIVE_WORKSHEET.equals(questionByUuid.type())) {
      return transformLiveWorkSheetToQuestion(questionByUuid);
    }
    return question;
  }

  private Question transformLiveWorkSheetToQuestion(QuestionDto.Question questionByUuid) {
    Question question = new Question();
    if (QuestionType.LIVE_WORKSHEET.equals(questionByUuid.type())) {
      var liveWsQuestion = questionByUuid.worksheet().getFirst();
      question.setId(Math.toIntExact(liveWsQuestion.id()));
      question.setQuestions(liveWsQuestion.workSheetQuestion().question());
      question.setLiveWorksheetAnswer(liveWsQuestion.workSheetQuestion().answer());
      question.setLiveWorksheetAnswerUuid(liveWsQuestion.workSheetQuestion().answerUuid());
      question.setLiveWorksheetAnswerType(liveWsQuestion.workSheetQuestion().workSheetAnswerType());
      question.setMarks(liveWsQuestion.workSheetQuestion().marks());
      question.setUuid(liveWsQuestion.uuid());
      question.setType(liveWsQuestion.workSheetQuestion().workSheetQuestionType().toString());
    }
    return question;
  }

  public String getExplanation(QuestionDto.Question questionByUuid) {
    if (questionByUuid == null) {
      return null;
    }
    if (Objects.nonNull(questionByUuid.explanation())) {
      return questionByUuid.explanation();
    }
    return questionByUuid.subjective() != null ? questionByUuid.subjective().explanation() : null;
  }

  public void addElaborateAnswerIfMcqQuestion(
      QuestionDto.Question questionByUuid, ExamQuestion examQuestion, ExamAnswer examAnswer) {
    if (("mcq").equalsIgnoreCase(questionByUuid.type().toString())) {
      MultipleChoiceQuestionWithAnswer mcqQuestion =
          (MultipleChoiceQuestionWithAnswer)
              contentService.getQuestionBySubjectSlugAndUuid(
                  bearerToken,
                  questionByUuid.type(),
                  questionByUuid.subjectSlug(),
                  questionByUuid.uuid(),
                  true);

      examQuestion.setElaborateAnswer(mcqQuestion.getElaborateAnswer(mcqQuestion.getAnswer()));

      if (examAnswer.getAnswer() != null && !Objects.equals(examAnswer.getAnswer(), "0")) {
        examQuestion.setElaborateSelectedAnswer(
            mcqQuestion.getElaborateAnswer(mcqQuestion.getAnswer()));
      }
    }
  }

  private ExamDetails buildExamDetails(Exam exam, double percentageSecured) {
    final var examDetails =
        ExamDetails.builder()
            .noOfCorrectAnswer(exam.getCorrectAnswers())
            .percentageSecured(percentageSecured)
            .isGoalAchieved(false)
            .examId(exam.getId())
            .noOfQuestions(exam.getNoOfQuestions())
            .corrected(exam.isCorrected())
            .build();
    final var testDefinition = examService.getTestDefinitionForAnExam(exam);
    if (Objects.nonNull(testDefinition)) {
      examDetails.setTestName(testDefinition.getTestName());
      examDetails.setGrade(testDefinition.getGradeSlug());
      examDetails.setSubject(testDefinition.getSubjectSlug());
    }

    return examDetails;
  }

  public StudentAnswerPracticeResponse submitPracticeAnswer(
      StudentAnswerPracticeRequest studentAnswerRequest, String bearerToken) {
    log.debug("Add New Practice Answer service called");
    var examAnswer = new ExamAnswer();
    var exam = examService.findById(studentAnswerRequest.getExamId());
    var examAns =
        studentAnswerRepository.findByExamAndQuestionId(exam, studentAnswerRequest.getQuestionId());
    if (examAns.isPresent()) {
      return null;
    }
    var selectedOption = getSelectedOption(studentAnswerRequest);
    StudentAnswerPracticeResponse studentAnswerResponse =
        StudentAnswerPracticeResponse.builder()
            .examDetails(
                ExamQuestionPractice.builder()
                    .examId(studentAnswerRequest.getExamId())
                    .answer(studentAnswerRequest.getAnswer())
                    .selectedAnswer(selectedOption)
                    .questionUuid(studentAnswerRequest.getQuestionUuid())
                    .questionId(studentAnswerRequest.getQuestionId())
                    .type(studentAnswerRequest.getType())
                    .build())
            .build();

    try {
      final var questionType = QuestionType.getByType(studentAnswerRequest.getType());
      var questionByUuid =
          getContentServiceQuestionByUuid(
              bearerToken, exam, questionType, studentAnswerRequest.getQuestionUuid());
      boolean isMcqQuestion = QuestionType.MCQ.equals(questionType);

      boolean isCorrect = checkIsCorrect(isMcqQuestion, questionByUuid, studentAnswerRequest);

      examAnswer.setExamReference(exam.getId());
      examAnswer.setIsMobile(authService.isUserLoginByMobile());
      examAnswer.setExam(examService.findById(studentAnswerRequest.getExamId()));
      examAnswer.setActive(true);
      examAnswer.setSelectedOption(selectedOption);
      examAnswer.setAnswer(studentAnswerRequest.getAnswer());
      examAnswer.setQuestionId(studentAnswerRequest.getQuestionId());
      examAnswer.setQuestionUuid(studentAnswerRequest.getQuestionUuid());
      examAnswer.setCorrect(isCorrect);
      examAnswer.setType(questionType.getType());
      examAnswer.setMarksPerQuestion(questionByUuid.getMarks());
      examAnswer.setMarksScoredPerQuestion(
          isMcqQuestion && isCorrect ? (float) questionByUuid.getMarks() : 0);
      examAnswer.setSubtopicSlug(questionByUuid.getSubtopicSlug());

      if (examAnswer.getSelectedOption() != 0) {
        studentAnswerResponse
            .getExamDetails()
            .setElaborateAnswer(
                ((MultipleChoiceQuestionWithAnswer) questionByUuid)
                    .getElaborateAnswer(
                        ((MultipleChoiceQuestionWithAnswer) questionByUuid).getAnswer()));
      }

      studentAnswerResponse.getExamDetails().setCorrect(isCorrect);
      studentAnswerResponse.getExamDetails().setQuestionDetails(questionByUuid);
      studentAnswerRepository.save(examAnswer);
      examRevisionEventPublisher.publishEvent(
          ExamRevisionRequest.builder()
              .student(exam.getStudent())
              .exam(exam)
              .questionUuid(questionByUuid.getUuid())
              .isCorrect(isCorrect)
              .build());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return studentAnswerResponse;
  }

  private int getSelectedOption(StudentAnswerPracticeRequest studentAnswerRequest) {
    return studentAnswerRequest.getPermKey() == null || studentAnswerRequest.getPermKey().isEmpty()
        ? studentAnswerRequest.getSelectedAnswer()
        : getPermutationAnswer(studentAnswerRequest);
  }

  private boolean checkIsCorrect(
      boolean isMcqQuestion,
      Question questionByUuid,
      StudentAnswerPracticeRequest studentAnswerRequest) {
    if (studentAnswerRequest.getPermKey() == null || studentAnswerRequest.getPermKey().isEmpty()) {
      return isMcqQuestion
          && isAnswerCorrectWithoutPermutation(questionByUuid, studentAnswerRequest);
    }
    return isAnswerCorrectWithPermutation(questionByUuid, studentAnswerRequest);
  }

  private boolean isAnswerCorrectWithPermutation(
      Question questionByUuid, StudentAnswerPracticeRequest studentAnswerRequest) {
    int positionInPermutation = getPermutationAnswer(studentAnswerRequest);

    return ((MultipleChoiceQuestionWithAnswer) questionByUuid)
        .getAnswer()
        .equals(positionInPermutation);
  }

  private int getPermutationAnswer(StudentAnswerPracticeRequest studentAnswerRequest) {
    List<String> permutation =
        randomOptionGenerator.getPermutationByKey(studentAnswerRequest.getPermKey());
    if (permutation != null
        && isValidSelectedAnswer(studentAnswerRequest.getSelectedAnswer(), permutation.size())) {
      String selectedOption = permutation.get(studentAnswerRequest.getSelectedAnswer() - 1);
      return convertAnswerToOption(selectedOption);
    }
    return 0;
  }

  private boolean isAnswerCorrectWithoutPermutation(
      Question questionByUuid, StudentAnswerPracticeRequest studentAnswerRequest) {
    return ((MultipleChoiceQuestionWithAnswer) questionByUuid)
        .getAnswer()
        .equals(studentAnswerRequest.getSelectedAnswer());
  }

  private boolean isValidSelectedAnswer(Integer selectedAnswer, int size) {
    return selectedAnswer >= 1 && selectedAnswer <= size;
  }

  private Integer convertAnswerToOption(String selectedAnswer) {
    return switch (selectedAnswer) {
      case "A" -> 1;
      case "B" -> 2;
      case "C" -> 3;
      case "D" -> 4;
      default -> throw new IllegalArgumentException("Invalid selectedAnswer: " + selectedAnswer);
    };
  }

  public int countNoOfQuestion(long examId) {
    return studentAnswerRepository.countByExamId(examId);
  }

  public boolean isSubjectiveQuestionExists(long examId) {
    return studentAnswerRepository.isSubjectiveQuestionExists(examId);
  }

  private QuestionDto.SearchQuestionResponse buildSearchQuestionResponse(
      Question assignmentQuestion) {
    var question =
        QuestionDto.Question.builder()
            .id(assignmentQuestion.getId())
            .uuid(assignmentQuestion.getUuid())
            .question(assignmentQuestion.getQuestions())
            .marks(assignmentQuestion.getMarks())
            .chapterSlug(assignmentQuestion.getChapterSlug())
            .complexity(assignmentQuestion.getComplexity())
            .active(assignmentQuestion.isActive())
            .organizationSlug(assignmentQuestion.getOrganizationSlug())
            .organization(assignmentQuestion.getOrganization())
            .type(QuestionType.getByType(assignmentQuestion.getType()))
            .questionCategoryId(assignmentQuestion.getQuestionCategoryId())
            .build();
    return QuestionDto.SearchQuestionResponse.builder()
        .questions(Collections.singletonList(question))
        .build();
  }
}
