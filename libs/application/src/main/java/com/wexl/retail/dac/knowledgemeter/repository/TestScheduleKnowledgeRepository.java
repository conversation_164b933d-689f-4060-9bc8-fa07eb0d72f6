package com.wexl.retail.dac.knowledgemeter.repository;

import com.wexl.retail.dac.knowledgemeter.dto.OrgsTestKnowledgeResult;
import com.wexl.retail.dac.knowledgemeter.model.TestScheduleKnowledge;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface TestScheduleKnowledgeRepository
    extends JpaRepository<TestScheduleKnowledge, Long> {

  Optional<TestScheduleKnowledge> findByStudentIdAndSubTopicSlug(Long student, String subTopics);

  List<TestScheduleKnowledge> findAllByOrgSlugIn(List<String> org);

  List<TestScheduleKnowledge> findAllByOrgSlugInAndBoardAndGradeSlugAndSubjectSlug(
      List<String> org, String board, String grade, String subject);

  List<TestScheduleKnowledge> findAllByOrgSlugInAndBoardAndGradeSlug(
      List<String> childOrgs, String board, String grade);

  List<TestScheduleKnowledge> findAllByOrgSlugInAndBoard(List<String> childOrgs, String board);

  @Query(
      value =
          """
          select grade_slug ,subject_slug  ,(tskd.marks/tskd.total_marks)*100 as  kmPercentage
          from test_schedule_knowledge tsk
          inner join test_schedule_knowledge_details tskd  on tskd.test_schedule_knowledge_id  = tsk .id
          where tsk.org_slug  in (:childOrgs) and board = :board and grade_slug in (:gradeList) and subject_slug in (:subjectList)
          group  by grade_slug , subject_slug,marks,kmPercentage""",
      nativeQuery = true)
  List<OrgsTestKnowledgeResult> getSubjectWiseOrganizationKnowledgeMeter(
      List<String> childOrgs, String board, List<String> gradeList, List<String> subjectList);
}
