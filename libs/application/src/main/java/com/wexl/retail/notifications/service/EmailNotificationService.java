package com.wexl.retail.notifications.service;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.generic.ProfileUtils;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.msg91.service.Msg91SmsService;
import com.wexl.retail.notifications.dto.Message;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.*;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.repository.ScheduledEmailRepository;
import com.wexl.retail.notifications.repository.ScheduledMessageRepository;
import com.wexl.retail.notifications.resolver.RecipientResolver;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.whatsapp.WhatsAppService;
import com.wexl.retail.whatsapp.interakt.dto.Request;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class EmailNotificationService {
  private final OrgSettingsService orgSettingsService;
  private final MessageTemplateRepository messageTemplateRepository;
  private final UserRepository userRepository;
  private final OrganizationRepository organizationRepository;
  private final NotificationRepository notificationRepository;
  private final List<RecipientResolver> recipientResolvers;
  private final EmailService emailService;
  private final ScheduledEmailRepository scheduledEmailRepository;
  private final ProfileUtils profileUtils;
  private final StudentRepository studentRepository;
  private final ReportCardService reportCardService;
  private final NotificationsService notificationsService;
  private final Msg91SmsService msg91SmsService;
  private final ScheduledMessageRepository scheduledMessageRepository;
  private final WhatsAppService whatsAppService;
  private final StorageService storageService;
  private final TeacherRepository teacherRepository;
  private final Environment environment;
  private final ClassroomService classroomService;

  @Value("${app.allowed.test.orgSlug:}")
  private String allowedTestOrgSlug;

  public void createEmailNotification(
      String orgSlug, NotificationDto.EmailRequest emailRequest, String teacherAuthId) {
    Organization organization = orgSettingsService.validateOrganizaiton(orgSlug);
    var messageTemplate = getMessageTemplate(emailRequest.messageTemplateId());
    updateMessageTemplate(messageTemplate, emailRequest);

    var sendTo = emailRequest.sendTo();
    if (sendTo == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }

    List<Object> recipients =
        recipientResolvers.stream()
            .filter(resolver -> resolver.supports(sendTo))
            .flatMap(resolver -> resolver.resolveRecipients(orgSlug, sendTo).stream())
            .toList();

    List<NotificationDto.EmailTo> emailRecipients = buildEmailRecipients(recipients);

    boolean canSendEmail =
        (!profileUtils.isDev() || allowedTestOrgSlug.equals(orgSlug))
            && StringUtils.isNotEmpty(messageTemplate.getEmailTemplateId())
            && !emailRecipients.isEmpty();

    log.info(
        "Organization Slug is={} , Email sending eligibility: canSendEmail={}, isDevProfile={}, isAllowedTestOrgSlug={}, emailTemplateIdPresent={}, emailRecipientsNotEmpty={}",
        orgSlug,
        canSendEmail,
        profileUtils.isDev(),
        allowedTestOrgSlug.equals(orgSlug),
        StringUtils.isNotEmpty(messageTemplate.getEmailTemplateId()),
        !emailRecipients.isEmpty());

    if ("NOW".equals(emailRequest.sendType()) && canSendEmail) {
      for (NotificationDto.EmailTo recipient : emailRecipients) {
        emailService.sendEmailNotification(organization, recipient, messageTemplate);
        log.info("Email sent to {} email", recipient.email());
      }
    } else if ("SCHEDULE".equals(emailRequest.sendType())
        && emailRequest.scheduledDateTime() != null
        && StringUtils.isNotEmpty(messageTemplate.getEmailTemplateId())
        && !emailRecipients.isEmpty()) {
      scheduleEmailSend(
          organization, messageTemplate, emailRecipients, emailRequest.scheduledDateTime());
    }
    saveNotificationEntities(recipients, emailRequest, orgSlug, messageTemplate, teacherAuthId);
  }

  public void scheduleEmailSend(
      Organization organization,
      MessageTemplate messageTemplate,
      List<NotificationDto.EmailTo> emailRecipients,
      LocalDateTime scheduledTime) {
    List<ScheduledEmail> scheduledEmails = new ArrayList<>();

    for (NotificationDto.EmailTo recipient : emailRecipients) {
      ScheduledEmail scheduledEmail = new ScheduledEmail();
      scheduledEmail.setOrgSlug(organization.getSlug());
      scheduledEmail.setRecipientEmail(recipient.email());
      scheduledEmail.setRecipientName(recipient.name());
      scheduledEmail.setSubject("Email Notification");
      scheduledEmail.setMessage(messageTemplate.getMessage());
      scheduledEmail.setScheduledTime(scheduledTime);
      scheduledEmail.setSent(false);
      if (messageTemplate.getAttachment() != null) {
        scheduledEmail.setAttachmentUrl(messageTemplate.getAttachment());
      }

      scheduledEmails.add(scheduledEmail);
    }
    scheduledEmailRepository.saveAll(scheduledEmails);
  }

  public void processDueEmails(List<ScheduledEmail> dueEmails) {
    log.info("email job started");

    for (ScheduledEmail email : dueEmails) {
      try {
        Organization organization = orgSettingsService.validateOrganizaiton(email.getOrgSlug());

        NotificationDto.EmailTo recipient =
            NotificationDto.EmailTo.builder()
                .email(email.getRecipientEmail())
                .name(email.getRecipientName())
                .build();
        MessageTemplate template = new MessageTemplate();
        template.setMessage(email.getMessage());
        template.setAttachment(email.getAttachmentUrl());

        emailService.sendEmailNotification(organization, recipient, template);

        email.setSent(true);
        scheduledEmailRepository.save(email);

      } catch (Exception e) {
        log.error("Error sending scheduled email to {}", email.getRecipientEmail(), e);
      }
    }

    log.info("email job ended");
  }

  private void updateMessageTemplate(
      MessageTemplate messageTemplate, NotificationDto.EmailRequest emailRequest) {
    messageTemplate.setTitle(emailRequest.title());
    messageTemplate.setMessage(emailRequest.message());
    messageTemplate.setAttachment(emailRequest.attachment());
    messageTemplateRepository.save(messageTemplate);
  }

  private List<NotificationDto.EmailTo> buildEmailRecipients(List<Object> recipients) {
    List<NotificationDto.EmailTo> emailRecipients = new ArrayList<>();
    List<Student> students =
        recipients.stream().filter(Student.class::isInstance).map(Student.class::cast).toList();

    List<Teacher> teachersRecipients =
        recipients.stream().filter(Teacher.class::isInstance).map(Teacher.class::cast).toList();

    List<Teacher> adminTeachers = new ArrayList<>();
    List<Teacher> teachers = new ArrayList<>();

    for (Teacher teacher : teachersRecipients) {
      if (UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())) {
        adminTeachers.add(teacher);
      } else {
        teachers.add(teacher);
      }
    }

    // Students
    emailRecipients.addAll(
        buildStudentEmailToRecipients(students).stream()
            .map(NotificationDto.StudentEmailRecipient::emailTo)
            .toList());

    // Teachers and Admins
    emailRecipients.addAll(buildTeacherEmailToRecipients(teachers));
    emailRecipients.addAll(buildTeacherEmailToRecipients(adminTeachers));

    return emailRecipients;
  }

  private void saveNotificationEntities(
      List<Object> recipients,
      NotificationDto.EmailRequest emailRequest,
      String orgSlug,
      MessageTemplate messageTemplate,
      String teacherAuthId) {

    var user = userRepository.findByAuthUserId(teacherAuthId);

    Notification notification = new Notification();
    notification.setOrgSlug(orgSlug);
    notification.setTitle(emailRequest.title());
    notification.setMessage(emailRequest.message());
    notification.setAttachments(
        emailRequest.attachment() != null ? List.of(emailRequest.attachment()) : null);
    notification.setMessageTemplate(messageTemplate);
    notification.setCreatedBy(user.get().getTeacherInfo());
    notification.setNotificationType(
        NotificationType.valueOf(emailRequest.notificationType().name()));
    notification.setOrganization(organizationRepository.findBySlug(orgSlug));
    notification.setFeature(CommunicationFeature.EMAIL);

    List<Student> students =
        recipients.stream().filter(Student.class::isInstance).map(Student.class::cast).toList();

    List<Teacher> teachers = new ArrayList<>();
    List<Teacher> adminTeachers = new ArrayList<>();

    List<Teacher> teachersRecipients =
        recipients.stream().filter(Teacher.class::isInstance).map(Teacher.class::cast).toList();

    for (Teacher teacher : teachersRecipients) {
      if (UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())) {
        adminTeachers.add(teacher);
      } else {
        teachers.add(teacher);
      }
    }

    // Student notifications
    List<StudentNotification> studentNotifications =
        students.stream()
            .map(
                student -> {
                  StudentNotification sn = new StudentNotification();
                  sn.setNotification(notification);
                  sn.setStudent(student);
                  sn.setOrgSlug(orgSlug);
                  return sn;
                })
            .toList();

    // Teacher notifications
    List<TeacherNotification> teacherNotifications =
        teachers.stream()
            .map(
                teacher -> {
                  TeacherNotification tn = new TeacherNotification();
                  tn.setNotification(notification);
                  tn.setTeacher(teacher);
                  tn.setOrgSlug(orgSlug);
                  return tn;
                })
            .toList();

    List<TeacherNotification> adminNotifications =
        adminTeachers.stream()
            .map(
                admin -> {
                  TeacherNotification atn = new TeacherNotification();
                  atn.setNotification(notification);
                  atn.setTeacher(admin);
                  atn.setOrgSlug(orgSlug);
                  return atn;
                })
            .toList();

    notification.setStudentNotifications(studentNotifications);

    List<TeacherNotification> combinedNotificationList = new ArrayList<>();
    combinedNotificationList.addAll(adminNotifications);
    combinedNotificationList.addAll(teacherNotifications);
    notification.setTeacherNotifications(combinedNotificationList);

    notificationRepository.save(notification);
  }

  public List<NotificationDto.StudentEmailRecipient> buildStudentEmailToRecipients(
      List<Student> students) {
    return students.stream()
        .filter(
            s ->
                s.getUserInfo() != null
                    && s.getUserInfo().getEmail() != null
                    && !s.getUserInfo().getEmail().isBlank())
        .map(
            s ->
                new NotificationDto.StudentEmailRecipient(
                    s.getId(),
                    null,
                    NotificationDto.EmailTo.builder()
                        .email(s.getUserInfo().getEmail())
                        .name(s.getUserInfo().getFirstName())
                        .build()))
        .toList();
  }

  public List<NotificationDto.EmailTo> buildTeacherEmailToRecipients(List<Teacher> teachers) {
    return teachers.stream()
        .filter(
            s ->
                s.getUserInfo() != null
                    && s.getUserInfo().getEmail() != null
                    && !s.getUserInfo().getEmail().isBlank())
        .map(
            s ->
                NotificationDto.EmailTo.builder()
                    .email(s.getUserInfo().getEmail())
                    .name(s.getUserInfo().getFirstName())
                    .build())
        .toList();
  }

  public void sendBirthdayEmailNotification(
      NotificationDto.EmailRequest request, String orgSlug, String teacherAuthId) {
    log.info("Birthday email job started for orgSlug: {}", orgSlug);

    Organization organization = orgSettingsService.validateOrganizaiton(orgSlug);
    var messageTemplate = getMessageTemplate(request.messageTemplateId());

    updateMessageTemplate(messageTemplate, request);

    List<Student> birthdayStudents = studentRepository.findBirthdayStudentsByOrgSlug(orgSlug);

    log.info("Total students with birthdays today: {}", birthdayStudents.size());

    boolean isAllowedEnv =
        !profileUtils.isDev() || organization.getSlug().equals(allowedTestOrgSlug);

    if (!birthdayStudents.isEmpty() && isAllowedEnv) {

      if (NotificationType.EMAIL.name().equals(request.notificationType().name())) {
        List<NotificationDto.StudentEmailRecipient> emailRecipientsData =
            buildStudentEmailToRecipients(birthdayStudents);

        if (!emailRecipientsData.isEmpty()) {
          List<NotificationDto.EmailTo> recipients =
              emailRecipientsData.stream()
                  .map(NotificationDto.StudentEmailRecipient::emailTo)
                  .toList();
          for (NotificationDto.EmailTo recipient : recipients) {
            log.info("Sending birthday email to: {}", recipient.email());
            emailService.sendBirthdayEmailNotification(organization, recipient, messageTemplate);
          }

          saveNotification(
              request,
              emailRecipientsData,
              messageTemplate,
              orgSlug,
              teacherAuthId,
              CommunicationFeature.EMAIL);
        } else {
          log.info("No email recipients found for birthday students.");
        }

      } else if (NotificationType.MESSAGE.name().equals(request.notificationType().name())) {
        List<NotificationDto.StudentEmailRecipient> whatsappRecipientsData =
            getStudentsMobileNumbers(birthdayStudents);

        if (!whatsappRecipientsData.isEmpty()) {
          log.info(
              "Sending birthday WhatsApp message to {} students", whatsappRecipientsData.size());
          sendWhatsapp(request, messageTemplate, whatsappRecipientsData, organization);

          saveNotification(
              request,
              whatsappRecipientsData,
              messageTemplate,
              orgSlug,
              teacherAuthId,
              CommunicationFeature.MESSAGE);
        } else {
          log.info("No WhatsApp recipients found for birthday students.");
        }

      } else {
        log.warn("Invalid notification type. Use either EMAIL or MESSAGE.");
      }

    } else {
      log.info(
          "Birthday notification skipped. Reason: No recipients found or not allowed in current environment. "
              + "Organization: {}, Recipients found: {}, Environment: {}, Allowed Test Org: {}",
          orgSlug,
          birthdayStudents.size(),
          Arrays.toString(environment.getActiveProfiles()),
          allowedTestOrgSlug);
    }

    log.info("Birthday notification job completed for orgSlug: {}", orgSlug);
  }

  public void saveNotification(
      NotificationDto.EmailRequest request,
      List<NotificationDto.StudentEmailRecipient> recipientsData,
      MessageTemplate messageTemplate,
      String orgSlug,
      String teacherAuthId,
      CommunicationFeature feature) {

    var user = userRepository.findByAuthUserId(teacherAuthId);
    Notification notification = new Notification();
    notification.setOrgSlug(orgSlug);
    notification.setTitle(request.title());
    notification.setMessage(request.message());
    notification.setAttachments(
        request.attachment() != null ? List.of(request.attachment()) : null);
    notification.setMessageTemplate(messageTemplate);
    notification.setCreatedBy(user.get().getTeacherInfo());
    notification.setNotificationType(NotificationType.valueOf(request.notificationType().name()));
    notification.setOrganization(organizationRepository.findBySlug(orgSlug));
    notification.setFeature(feature);

    List<StudentNotification> studentNotifications =
        recipientsData.stream()
            .filter(data -> data.studentId() != null)
            .map(
                data ->
                    studentRepository
                        .findById(data.studentId())
                        .map(
                            student -> {
                              StudentNotification sn = new StudentNotification();
                              sn.setNotification(notification);
                              sn.setStudent(student);
                              sn.setOrgSlug(orgSlug);
                              return sn;
                            }))
            .flatMap(Optional::stream)
            .toList();
    notification.setStudentNotifications(studentNotifications);

    List<TeacherNotification> teacherNotifications =
        recipientsData.stream()
            .filter(data -> data.teacherId() != null)
            .map(
                data ->
                    teacherRepository
                        .findById(data.teacherId())
                        .map(
                            teacher -> {
                              TeacherNotification tn = new TeacherNotification();
                              tn.setNotification(notification);
                              tn.setTeacher(teacher);
                              tn.setOrgSlug(orgSlug);
                              return tn;
                            }))
            .flatMap(Optional::stream)
            .toList();
    notification.setTeacherNotifications(teacherNotifications);

    notificationRepository.save(notification);
  }

  public void sendFeeDueEmailNotification(
      NotificationDto.EmailRequest request, String orgSlug, String teacherAuthId) {
    log.info("Starting fee due email notification for orgSlug: {}", orgSlug);

    Organization organization = orgSettingsService.validateOrganizaiton(orgSlug);
    var messageTemplate = getMessageTemplate(request.messageTemplateId());

    updateMessageTemplate(messageTemplate, request);

    List<Student> students = studentRepository.getFeeUnpaidStudentsByOrg(orgSlug, true);
    log.info("Total fee defaulters identified: {}", students.size());

    List<NotificationDto.StudentEmailRecipient> recipientsData =
        buildStudentEmailToRecipients(students);
    log.info("Valid email recipients found: {}", recipientsData.size());

    boolean isAllowedEnv =
        !profileUtils.isDev() || organization.getSlug().equals(allowedTestOrgSlug);

    if (!recipientsData.isEmpty() && isAllowedEnv) {

      for (NotificationDto.StudentEmailRecipient recipient : recipientsData) {

        log.info("Sending fee due email to: {}", recipient.emailTo().email());
        emailService.sendFeeDueEmailNotification(organization, recipient, messageTemplate);
      }

      saveNotification(
          request,
          recipientsData,
          messageTemplate,
          orgSlug,
          teacherAuthId,
          CommunicationFeature.EMAIL);
    } else {
      log.info("Skipped sending Due emails in dev profile for orgSlug: {}", orgSlug);
    }
    log.info("Fee due email notification process completed for orgSlug: {}", orgSlug);
  }

  public NotificationDto.EmailNotifications getEmailNotifications(String orgSlug) {
    var emailNotifications =
        notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
            orgSlug, CommunicationFeature.EMAIL);
    var notificationResponse =
        notificationsService.buildNotificationResponse(emailNotifications, 100);
    return NotificationDto.EmailNotifications.builder().notifications(notificationResponse).build();
  }

  private MessageTemplate getMessageTemplate(Long messageTemplateId) {
    return messageTemplateRepository
        .findById(messageTemplateId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidTemplate"));
  }

  public void sendMessages(
      NotificationDto.EmailRequest request, String orgSlug, String teacherAuthId) {
    Organization organization = orgSettingsService.validateOrganizaiton(orgSlug);
    MessageTemplate messageTemplate = getMessageTemplate(request.messageTemplateId());
    updateMessageTemplate(messageTemplate, request);

    var sendTo = request.sendTo();
    if (sendTo == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }

    List<Object> recipients =
        recipientResolvers.stream()
            .filter(resolver -> resolver.supports(sendTo))
            .flatMap(resolver -> resolver.resolveRecipients(orgSlug, sendTo).stream())
            .toList();

    List<NotificationDto.StudentEmailRecipient> validRecipients =
        extractMessageRecipients(recipients);

    if (validRecipients == null || validRecipients.isEmpty()) {
      log.warn("No valid recipients provided. Skipping message processing.");
      return;
    }

    List<Message> sendMessages = request.sendMessage();
    if ("SCHEDULE".equals(request.sendType())) {
      if (request.scheduledDateTime() != null) {
        saveScheduleMessages(organization, validRecipients, request, messageTemplate, false);
      } else {
        log.warn("Scheduled send requested, but scheduledDateTime is missing.");
      }
      return;
    }

    if (sendMessages.contains(Message.SMS)) {
      sendSms(request, messageTemplate, validRecipients, organization);
    }

    if (sendMessages.contains(Message.WHATSAPP)) {
      sendWhatsapp(request, messageTemplate, validRecipients, organization);
    }

    saveNotification(
        request,
        validRecipients,
        messageTemplate,
        orgSlug,
        teacherAuthId,
        CommunicationFeature.MESSAGE);
  }

  private List<NotificationDto.StudentEmailRecipient> extractMessageRecipients(
      List<Object> recipients) {
    List<Student> students =
        recipients.stream().filter(Student.class::isInstance).map(Student.class::cast).toList();

    List<Teacher> adminTeachers = new ArrayList<>();
    List<Teacher> teachers = new ArrayList<>();

    List<Teacher> teachersRecipients =
        recipients.stream().filter(Teacher.class::isInstance).map(Teacher.class::cast).toList();

    teachersRecipients.forEach(
        teacher -> {
          if (UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())) {
            adminTeachers.add(teacher);
          } else {
            teachers.add(teacher);
          }
        });

    List<NotificationDto.StudentEmailRecipient> studentMessageRecipients =
        getStudentsMobileNumbers(students);
    List<NotificationDto.StudentEmailRecipient> teacherMessageRecipients =
        getTeacherMobileNumbers(teachers);
    List<NotificationDto.StudentEmailRecipient> adminMessageRecipients =
        getTeacherMobileNumbers(adminTeachers);

    List<NotificationDto.StudentEmailRecipient> combined = new ArrayList<>();
    combined.addAll(studentMessageRecipients);
    combined.addAll(teacherMessageRecipients);
    combined.addAll(adminMessageRecipients);

    return combined;
  }

  private void sendWhatsapp(
      NotificationDto.EmailRequest request,
      MessageTemplate messageTemplate,
      List<NotificationDto.StudentEmailRecipient> messageRecipients,
      Organization organization) {

    if (!"NOW".equals(request.sendType())) {
      return;
    }

    if (StringUtils.isNotEmpty(messageTemplate.getWhatsAppTemplateId())
        && !messageRecipients.isEmpty()
        && (!profileUtils.isDev() || organization.getSlug().equals(allowedTestOrgSlug))) {

      List<Request.Recipient> recipients =
          buildWhatsappRecipient(messageRecipients, organization, messageTemplate);
      whatsAppService.sendBulkWhatsAppMessageWithStaticMessage(
          messageTemplate.getWhatsAppTemplateId(), recipients);
    }
  }

  private List<Request.Recipient> buildWhatsappRecipient(
      List<NotificationDto.StudentEmailRecipient> messageRecipients,
      Organization organization,
      MessageTemplate messageTemplate) {
    String currentDate = new SimpleDateFormat("dd-MM-yyyy").format(new Date());

    List<NotificationDto.EmailTo> recipients =
        messageRecipients.stream().map(NotificationDto.StudentEmailRecipient::emailTo).toList();

    String preSignedUrlForFetch;
    if (messageTemplate.getAttachment() != null) {
      preSignedUrlForFetch =
          storageService.generatePreSignedUrlForFetch(messageTemplate.getAttachment());
    } else {
      preSignedUrlForFetch = null;
    }

    return recipients.stream()
        .map(
            recipient ->
                Request.Recipient.builder()
                    .orgName(organization.getName())
                    .orgSlug(organization.getSlug())
                    .mobileNumber(recipient.mobileNumber())
                    .name(recipient.name())
                    .date(currentDate)
                    .reportCardLink(preSignedUrlForFetch)
                    .sectionName(recipient.sectionName())
                    .build())
        .toList();
  }

  private List<Msg91Dto.Recipient> buildRecipients(
      List<NotificationDto.StudentEmailRecipient> validRecipients, Organization organization) {
    String currentDate = new SimpleDateFormat("dd-MM-yyyy").format(new Date());

    return validRecipients.stream()
        .flatMap(
            recipient -> {
              List<Msg91Dto.Recipient> result = new ArrayList<>();

              Optional<Student> studentOpt = Optional.empty();
              Optional<Teacher> teacherOpt = Optional.empty();

              if (recipient.studentId() != null) {
                studentOpt = studentRepository.findById(recipient.studentId());
              }

              if (recipient.teacherId() != null) {
                teacherOpt = teacherRepository.findById(recipient.teacherId());
              }
              NotificationDto.EmailTo emailTo = recipient.emailTo();

              if (studentOpt.isPresent()) {
                Student student = studentOpt.get();
                String teacherName =
                    teacherOpt.map(t -> t.getUserInfo().getFirstName()).orElse(null);
                String sectionName =
                    student.getSection() != null ? student.getSection().getName() : null;

                result.add(
                    Msg91Dto.Recipient.builder()
                        .mobiles(emailTo.mobileNumber())
                        .name(emailTo.name())
                        .teacherName(teacherName)
                        .sectionName(sectionName)
                        .orgname(organization.getName())
                        .date(currentDate)
                        .build());
              } else if (teacherOpt.isPresent()) {
                result.add(
                    Msg91Dto.Recipient.builder()
                        .mobiles(emailTo.mobileNumber())
                        .name(emailTo.name())
                        .teacherName(null)
                        .sectionName(null)
                        .orgname(organization.getName())
                        .date(currentDate)
                        .build());
              }

              return result.stream();
            })
        .toList();
  }

  private void sendSms(
      NotificationDto.EmailRequest request,
      MessageTemplate messageTemplate,
      List<NotificationDto.StudentEmailRecipient> messageRecipients,
      Organization organization) {

    if (!"NOW".equals(request.sendType())) {
      return;
    }

    if (StringUtils.isNotEmpty(messageTemplate.getSmsDltTemplateId())
        && !messageRecipients.isEmpty()
        && (!profileUtils.isDev() || organization.getSlug().equals(allowedTestOrgSlug))) {

      List<Msg91Dto.Recipient> recipients = buildRecipients(messageRecipients, organization);
      msg91SmsService.sendBulkMessage(messageTemplate.getSmsDltTemplateId(), recipients);
    }
  }

  public void saveScheduleMessages(
      Organization organization,
      List<NotificationDto.StudentEmailRecipient> recipients,
      NotificationDto.EmailRequest request,
      MessageTemplate messageTemplate,
      boolean isFeeDue) {

    String currentDate = new SimpleDateFormat("dd-MM-yyyy").format(new Date());

    List<ScheduledMessage> scheduledMessages =
        recipients.stream()
            .map(
                recipient -> {
                  Optional<Student> studentOpt = Optional.empty();
                  Optional<Teacher> teacherOpt = Optional.empty();

                  if (recipient.studentId() != null) {
                    studentOpt = studentRepository.findById(recipient.studentId());
                  }
                  if (recipient.teacherId() != null) {
                    teacherOpt = teacherRepository.findById(recipient.teacherId());
                  }
                  NotificationDto.EmailTo emailTo = recipient.emailTo();
                  String sectionName =
                      studentOpt.map(Student::getSection).map(Section::getName).orElse(null);
                  String teacherName =
                      teacherOpt.map(t -> t.getUserInfo().getFirstName()).orElse(null);

                  return ScheduledMessage.builder()
                      .recipientName(emailTo.name())
                      .mobileNumber(emailTo.mobileNumber())
                      .orgSlug(organization.getSlug())
                      .sectionName(sectionName)
                      .teacherName(teacherName)
                      .date(currentDate)
                      .scheduledTime(request.scheduledDateTime())
                      .sent(false)
                      .messageType(request.sendMessage())
                      .attachment(messageTemplate.getAttachment())
                      .smsDltTemplateId(messageTemplate.getSmsDltTemplateId())
                      .whatsAppTemplateId(messageTemplate.getWhatsAppTemplateId())
                      .isFeeDueMessage(isFeeDue)
                      .build();
                })
            .toList();

    scheduledMessageRepository.saveAll(scheduledMessages);
  }

  public List<NotificationDto.StudentEmailRecipient> getStudentsMobileNumbers(
      List<Student> students) {
    return students.stream()
        .filter(
            s ->
                s.getUserInfo() != null
                    && s.getUserInfo().getMobileNumber() != null
                    && !s.getUserInfo().getMobileNumber().isBlank())
        .map(
            s ->
                new NotificationDto.StudentEmailRecipient(
                    s.getId(),
                    null,
                    NotificationDto.EmailTo.builder()
                        .mobileNumber(s.getUserInfo().getMobileNumber())
                        .name(s.getUserInfo().getFirstName())
                        .sectionName(s.getSection().getName())
                        .build()))
        .toList();
  }

  public List<NotificationDto.StudentEmailRecipient> getTeacherMobileNumbers(
      List<Teacher> teachers) {
    return teachers.stream()
        .filter(
            t ->
                t.getUserInfo() != null
                    && t.getUserInfo().getMobileNumber() != null
                    && !t.getUserInfo().getMobileNumber().isBlank())
        .map(
            t ->
                new NotificationDto.StudentEmailRecipient(
                    null,
                    t.getId(),
                    NotificationDto.EmailTo.builder()
                        .mobileNumber(t.getUserInfo().getMobileNumber())
                        .name(t.getUserInfo().getFirstName())
                        .build()))
        .toList();
  }

  public void processDueMessages(List<ScheduledMessage> scheduledMessages) {
    log.info("message job started");

    Map<String, List<Msg91Dto.Recipient>> smsRecipientsMap = new HashMap<>();

    Map<String, List<Request.Recipient>> whatsappRecipientsMap = new HashMap<>();

    Map<String, List<Request.Recipient>> feeDueWhatsappRecipientsMap = new HashMap<>();

    List<ScheduledMessage> markAsSent = new ArrayList<>();

    for (ScheduledMessage message : scheduledMessages) {
      try {
        if (message.getMessageType() != null) {
          if (message.getMessageType().contains(Message.SMS)) {
            String smsTemplateId = message.getSmsDltTemplateId();
            if (smsTemplateId != null && !smsTemplateId.isBlank()) {
              Msg91Dto.Recipient smsRecipient = buildSmsScheduledRecipient(message);
              smsRecipientsMap
                  .computeIfAbsent(smsTemplateId, k -> new ArrayList<>())
                  .add(smsRecipient);
            } else {
              log.warn("SMS template ID is null or blank for message: {}", message.getId());
            }
          }

          if (message.getMessageType().contains(Message.WHATSAPP)) {
            String whatsappTemplateId = message.getWhatsAppTemplateId();
            if (whatsappTemplateId != null && !whatsappTemplateId.isBlank()) {
              Request.Recipient whatsappRecipient = buildWhatsappScheduledRecipient(message);

              if (message.isFeeDueMessage()) {
                feeDueWhatsappRecipientsMap
                    .computeIfAbsent(whatsappTemplateId, k -> new ArrayList<>())
                    .add(whatsappRecipient);
              } else {
                whatsappRecipientsMap
                    .computeIfAbsent(whatsappTemplateId, k -> new ArrayList<>())
                    .add(whatsappRecipient);
              }
            } else {
              log.warn("WhatsApp template ID is null or blank for message: {}", message.getId());
            }
          }
        }

        message.setSent(true);
        markAsSent.add(message);
      } catch (Exception e) {
        log.error("Error preparing scheduled message for {}", message.getRecipientName(), e);
      }
    }

    smsRecipientsMap.forEach(
        (templateId, recipients) -> {
          msg91SmsService.sendBulkMessage(templateId, recipients);
          log.info("Sent {} SMS messages with template {}", recipients.size(), templateId);
        });

    feeDueWhatsappRecipientsMap.forEach(
        (templateId, recipients) -> {
          whatsAppService.sendFeeDueBulkWhatsAppMessage(templateId, recipients);
          log.info(
              "Sent {} WhatsApp FEE DUE messages with template {}", recipients.size(), templateId);
        });

    whatsappRecipientsMap.forEach(
        (templateId, recipients) -> {
          whatsAppService.sendBulkWhatsAppMessageWithStaticMessage(templateId, recipients);
          log.info("Sent {} WhatsApp messages with template {}", recipients.size(), templateId);
        });

    if (!markAsSent.isEmpty()) {
      scheduledMessageRepository.saveAll(markAsSent);
      log.info("Marked {} messages as sent", markAsSent.size());
    }

    log.info("message job ended");
  }

  private Request.Recipient buildWhatsappScheduledRecipient(ScheduledMessage message) {

    Organization organization = organizationRepository.findBySlug(message.getOrgSlug());
    String preSignedUrl =
        message.getAttachment() != null
            ? storageService.generatePreSignedUrlForFetch(message.getAttachment())
            : null;

    return Request.Recipient.builder()
        .name(message.getRecipientName())
        .date(message.getDate())
        .orgName(organization.getName())
        .mobileNumber(message.getMobileNumber())
        .sectionName(message.getSectionName())
        .reportCardLink(preSignedUrl)
        .build();
  }

  private Msg91Dto.Recipient buildSmsScheduledRecipient(ScheduledMessage message) {

    Organization organization = organizationRepository.findBySlug(message.getOrgSlug());

    return Msg91Dto.Recipient.builder()
        .mobiles(message.getMobileNumber())
        .name(message.getRecipientName())
        .teacherName(message.getTeacherName())
        .sectionName(message.getSectionName())
        .orgname(organization.getName())
        .date(message.getDate())
        .password(null)
        .link(null)
        .weblink(null)
        .applink(null)
        .build();
  }

  public NotificationDto.EmailNotifications getMessageNotifications(String orgSlug) {

    var messageNotifications =
        notificationRepository.findAllByOrgSlugAndFeatureOrderByCreatedAtDesc(
            orgSlug, CommunicationFeature.MESSAGE);
    var notificationResponse =
        notificationsService.buildNotificationResponse(messageNotifications, 100);
    return NotificationDto.EmailNotifications.builder().notifications(notificationResponse).build();
  }

  public void sendFeeDueMessageNotification(
      NotificationDto.EmailRequest request, String orgSlug, String teacherAuthId) {

    log.info("Starting fee due message notification for orgSlug: {}", orgSlug);

    Organization organization = orgSettingsService.validateOrganizaiton(orgSlug);
    var messageTemplate = getMessageTemplate(request.messageTemplateId());

    updateMessageTemplate(messageTemplate, request);

    List<Student> students = studentRepository.getFeeUnpaidStudentsByOrg(orgSlug, true);
    log.info("Total fee defaulters identified: {}", students.size());

    List<NotificationDto.StudentEmailRecipient> recipients = getStudentsMobileNumbers(students);

    if (recipients == null || recipients.isEmpty()) {
      log.warn("No recipients provided. Skipping message send.");
      return;
    }
    log.info("Valid message recipients found: {}", recipients.size());

    List<Message> sendMessages = request.sendMessage();
    if ("SCHEDULE".equals(request.sendType())) {
      if (request.scheduledDateTime() != null) {
        saveScheduleMessages(organization, recipients, request, messageTemplate, true);
      } else {
        log.warn("Scheduled send requested, but scheduledDateTime is missing.");
      }
    } else {
      if (sendMessages.contains(Message.WHATSAPP)
          && messageTemplate.getWhatsAppTemplateId() != null) {

        List<Request.Recipient> messageRecipients =
            buildWhatsappRecipient(recipients, organization, messageTemplate);
        whatsAppService.sendFeeDueBulkWhatsAppMessage(
            messageTemplate.getWhatsAppTemplateId(), messageRecipients);
      }
      if (sendMessages.contains(Message.SMS) && messageTemplate.getSmsDltTemplateId() != null) {
        sendSms(request, messageTemplate, recipients, organization);
      }
    }

    saveNotification(
        request, recipients, messageTemplate, orgSlug, teacherAuthId, CommunicationFeature.MESSAGE);

    log.info("Fee due message notification process completed for orgSlug: {}", orgSlug);
  }

  public List<StudentResponse> getBirthdayStudents(String orgSlug) {
    orgSettingsService.validateOrganizaiton(orgSlug);

    List<Student> birthdayStudents = studentRepository.findBirthdayStudentsByOrgSlug(orgSlug);
    log.info("Fetched {} birthday students in organization: {}", birthdayStudents.size(), orgSlug);

    return classroomService.getStudentResponsesByStudents(birthdayStudents);
  }

  public List<StudentResponse> getFeeDueStudents(String orgSlug) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    List<Student> students = studentRepository.getFeeUnpaidStudentsByOrg(orgSlug, true);
    log.info("Total fee defaulters identified: {}", students.size());

    return classroomService.getStudentResponsesByStudents(students);
  }
}
