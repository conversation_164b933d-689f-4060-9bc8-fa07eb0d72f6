package com.wexl.retail.test.schedule.dto;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupScheduleTestRequest {
  @NotNull private int duration;
  @NotNull private long testDefinitionId;
  @NotNull private long startDate;
  @NotNull private long endDate;
  @NotNull private String board;
  @NotNull private String grade;
  @NotNull private List<String> childOrgs = new ArrayList<>();
  private List<String> boards;
  private Set<Long> studentIds;
  private List<Long> sectionIds;
}
