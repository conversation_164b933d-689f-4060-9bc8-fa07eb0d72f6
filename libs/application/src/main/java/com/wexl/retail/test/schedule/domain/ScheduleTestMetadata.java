package com.wexl.retail.test.schedule.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTestMetadata {
  private List<String> sections;
  private String grade;
  private String board;
  private List<String> childOrgs = new ArrayList<>();
}
