package com.wexl.retail.metrics;

import java.util.List;

public class MetricTagList {
  private MetricTagList() {}

  static List<MetricTag> of() {
    return List.of();
  }

  public static List<MetricTag> of(String name, String value) {
    return List.of(new MetricTag(name, value));
  }

  static List<MetricTag> of(String name1, String value1, String name2, String value2) {
    return List.of(new MetricTag(name1, value1), new MetricTag(name2, value2));
  }

  public static List<MetricTag> of(
      String name1, String value1, String name2, String value2, String name3, String value3) {
    return List.of(
        new MetricTag(name1, value1), new MetricTag(name2, value2), new MetricTag(name3, value3));
  }
}
