package com.wexl.retail.mlp.service;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.mapper.ExamRecordsMapper;
import com.wexl.retail.mlp.model.KMData;
import com.wexl.retail.mlp.model.StudentKMeter;
import com.wexl.retail.mlp.repository.KMeterRepository;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.mlp.repository.StudentKMeterRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class KMeterService {

  private final KMeterRepository kMeterRepository;
  private final OrganizationRepository organizationRepository;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;
  private final ExamRepository examRepository;
  private final StrapiService strapiService;
  private final MlpRepository mlpRepository;

  private final StudentKMeterRepository studentKMeterRepository;

  public KMSummaryResponse getKmSummary(
      List<KMGradesRequest> gradesRequest, String orgSlug, String board) {

    List<KMData> gradeWisePerformanceResponse = new ArrayList<>();
    var gradeSlugs = gradesRequest.stream().map(KMGradesRequest::getGradeSlug).toList();

    var gradeEntity = strapiService.getAllGrades();
    Map<String, String> gradeMap = new HashMap<>();

    gradeEntity.forEach(entity -> gradeMap.put(entity.getSlug(), entity.getName()));
    var kmSummaryList = kMeterRepository.getAttendanceSummary(orgSlug, gradeSlugs, board);
    var organization = organizationRepository.findBySlug(orgSlug);
    for (var data : gradesRequest) {
      String attendancePercentage = "-";
      String knowledgePercentage = "-";

      var kmData =
          kmSummaryList.stream()
              .filter(s -> s.getGradeSlug().equals(data.getGradeSlug()))
              .findFirst();
      if (kmData.isPresent()) {
        attendancePercentage =
            String.valueOf(
                Double.parseDouble(
                    Constants.DECIMAL_FORMAT.format(kmData.get().getAttendancePercentage())));
        knowledgePercentage =
            String.valueOf(
                Double.parseDouble(
                    Constants.DECIMAL_FORMAT.format(kmData.get().getKnowledgePercentage())));
      }

      gradeWisePerformanceResponse.add(
          KMData.builder()
              .name(gradeMap.get(data.getGradeSlug()))
              .slug(data.getGradeSlug())
              .percentage(knowledgePercentage)
              .attendance(attendancePercentage)
              .build());
    }
    var totalAttendance = calculateTotalAttendancePercentage(kmSummaryList);
    return KMSummaryResponse.builder()
        .averageKnowledgePercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(totalAttendance.getKnowledgePercentage())))
        .averageAttendancePercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(totalAttendance.getAttendancePercentage())))
        .data(gradeWisePerformanceResponse)
        .orgName(organization.getName())
        .build();
  }

  public List<KMSummaryResponse> getKmGrades(String orgSlug, String board, String grades) {
    var kmSummaryList = kMeterRepository.getKmGrades(orgSlug, board, grades);
    var sectionList = kmSummaryList.stream().map(KmSummary::getName).distinct().toList();
    List<KMSummaryResponse> responseList = new ArrayList<>();

    for (var section : sectionList) {
      var kmSummaryData = kmSummaryList.stream().filter(s -> s.getName().equals(section)).toList();
      if (!kmSummaryData.isEmpty()) {
        List<KMData> kmData =
            kmSummaryData.stream()
                .map(
                    s ->
                        KMData.builder()
                            .name(s.getSubjectName())
                            .slug(s.getSubjectSlug())
                            .percentage(
                                String.valueOf(
                                    Double.parseDouble(
                                        Constants.DECIMAL_FORMAT.format(
                                            s.getKnowledgePercentage()))))
                            .attendance(
                                String.valueOf(
                                    Double.parseDouble(
                                        Constants.DECIMAL_FORMAT.format(
                                            s.getAttendancePercentage()))))
                            .build())
                .toList();

        var totalAttendance = calculateTotalAttendancePercentage(kmSummaryData);
        responseList.add(
            KMSummaryResponse.builder()
                .name(section)
                .averageKnowledgePercentage(
                    Double.parseDouble(
                        Constants.DECIMAL_FORMAT.format(totalAttendance.getKnowledgePercentage())))
                .averageAttendancePercentage(
                    Double.parseDouble(
                        Constants.DECIMAL_FORMAT.format(totalAttendance.getAttendancePercentage())))
                .data(kmData)
                .build());
      }
    }
    return responseList;
  }

  public MlpKnowledgeAndAttendanceData calculateTotalAttendancePercentage(
      List<KmSummary> kmSummaryData) {
    double knowledgePercentage = 0.0;
    double sectionAttendance = 0.0;
    double totalSectionPercentage = 0.0;
    double totalKnowledgePercentage = 0.0;
    MlpKnowledgeAndAttendanceData mlp = new MlpKnowledgeAndAttendanceData();
    if (!kmSummaryData.isEmpty()) {
      for (var data : kmSummaryData) {
        knowledgePercentage += data.getKnowledgePercentage();
        sectionAttendance += data.getAttendancePercentage();
      }
      totalSectionPercentage = sectionAttendance / kmSummaryData.size();
      totalKnowledgePercentage = knowledgePercentage / kmSummaryData.size();
    }
    mlp.setAttendancePercentage(totalSectionPercentage);
    mlp.setKnowledgePercentage(totalKnowledgePercentage);

    return mlp;
  }

  public List<SubjectDetailResponse> getKmSectionSummary(String orgSlug, String sectionUuid) {
    var kmSummaryList = kMeterRepository.getKmSectionSummary(orgSlug, sectionUuid);
    var subjectList = kmSummaryList.stream().map(KmSummary::getSubjectSlug).distinct().toList();
    List<SubjectDetailResponse> subjectDetailResponseList = new ArrayList<>();

    for (var subject : subjectList) {
      var subjectData =
          kmSummaryList.stream().filter(s -> s.getSubjectSlug().equals(subject)).toList();
      var chapterNamesList =
          subjectData.stream().map(KmSummary::getChapterName).distinct().toList();

      var totalAttendancePercentage =
          Double.parseDouble(
              Constants.DECIMAL_FORMAT.format(
                  (subjectData.stream().mapToDouble(KmSummary::getAttendancePercentage).sum())
                      / subjectData.size()));

      var totalKnowledgePercentage =
          Double.parseDouble(
              Constants.DECIMAL_FORMAT.format(
                  (subjectData.stream().mapToDouble(KmSummary::getKnowledgePercentage).sum())
                      / subjectData.size()));
      var chapterList = calculateChapterData(chapterNamesList, subjectData);
      subjectDetailResponseList.add(
          SubjectDetailResponse.builder()
              .name(subjectData.getFirst().getSubjectName())
              .slug(subject)
              .attendancePercentage(totalAttendancePercentage)
              .percentage(totalKnowledgePercentage)
              .chapterSummaryList(chapterList)
              .build());
    }
    return subjectDetailResponseList;
  }

  public List<GenericDto> calculateSubTopic(
      List<String> subtopicList, List<KmSummary> chapterList) {
    List<GenericDto> dtoList = new ArrayList<>();
    for (var subTopic : subtopicList) {
      var data =
          chapterList.stream()
              .filter(s -> Objects.equals(s.getSubtopicName(), subTopic))
              .findFirst();
      dtoList.add(
          GenericDto.builder()
              .name(Objects.nonNull(subTopic) ? subTopic : "")
              .slug(Objects.nonNull(subTopic) ? subTopic : "")
              .attendance(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          data.isPresent() ? data.get().getAttendancePercentage() : 0.0)))
              .average(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          data.isPresent() ? data.get().getKnowledgePercentage() : 0.0)))
              .build());
    }
    return dtoList;
  }

  public List<ChapterSummaryDto> calculateChapterData(
      List<String> chapterNamesList, List<KmSummary> subjectData) {
    List<ChapterSummaryDto> chapterSummaryDtoList = new ArrayList<>();
    for (var chapter : chapterNamesList) {
      var chapterList =
          subjectData.stream().filter(s -> s.getChapterName().equals(chapter)).toList();
      var subtopicList = chapterList.stream().map(KmSummary::getSubtopicName).toList();
      var dtoList = calculateSubTopic(subtopicList, chapterList);
      chapterSummaryDtoList.add(
          ChapterSummaryDto.builder()
              .name(chapter)
              .attendance(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          (dtoList.stream().mapToDouble(GenericDto::getAttendance).sum())
                              / dtoList.size())))
              .average(
                  Double.parseDouble(
                      Constants.DECIMAL_FORMAT.format(
                          (dtoList.stream().mapToDouble(GenericDto::getAverage).sum())
                              / dtoList.size())))
              .subtopicData(dtoList)
              .build());
    }
    return chapterSummaryDtoList;
  }

  public KMSummaryResponse getSubjectWisePerformanceOfStudent(
      String authUserId, List<String> subjectSlugs) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    Student student = studentRepository.findByUserId(user.getId());
    List<KMData> subjectWisePerformanceResponse = new ArrayList<>();
    var studentAttendanceList =
        kMeterRepository.getSubjectWiseAttendancePercentage(student.getId(), subjectSlugs);
    var subjects = strapiService.getAllSubjects();
    Map<String, String> subjectMap = new HashMap<>();
    subjects.forEach(
        data -> {
          if (subjectSlugs.contains(data.getSlug())) subjectMap.put(data.getSlug(), data.getName());
        });

    var studentKnowledgeList =
        kMeterRepository.getSubjectWiseKnowledgePercentage(student.getId(), subjectSlugs);
    Double attendanceCount = 0.0;
    Double knowledgeCount = 0.0;
    for (var subjectSlug : subjectSlugs) {

      var attendanceData =
          studentAttendanceList.stream()
              .filter(d -> d.getSubjectSlug().equals(subjectSlug))
              .findFirst();

      var knowledgeData =
          studentKnowledgeList.stream()
              .filter(d -> d.getSubjectSlug().equals(subjectSlug))
              .findFirst();
      String attendancePercentage = "-";
      String knowledgePercentage = "-";

      if (attendanceData.isPresent()) {
        attendancePercentage =
            String.valueOf(
                Double.parseDouble(
                    Constants.DECIMAL_FORMAT.format(
                        attendanceData.get().getAttendancePercentage())));
        attendanceCount += 1;
      }
      if (knowledgeData.isPresent()) {
        knowledgePercentage =
            String.valueOf(
                Double.parseDouble(
                    Constants.DECIMAL_FORMAT.format(knowledgeData.get().getKnowledgePercentage())));
        knowledgeCount += 1;
      }
      subjectWisePerformanceResponse.add(
          KMData.builder()
              .name(subjectMap.get(subjectSlug))
              .slug(subjectSlug)
              .percentage(knowledgePercentage)
              .attendance(attendancePercentage)
              .build());
    }

    var totalAttendance =
        calculateTotalPercentage(subjectWisePerformanceResponse, attendanceCount, knowledgeCount);

    return KMSummaryResponse.builder()
        .averageKnowledgePercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(totalAttendance.getKnowledgePercentage())))
        .averageAttendancePercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(totalAttendance.getAttendancePercentage())))
        .data(subjectWisePerformanceResponse)
        .build();
  }

  public MlpKnowledgeAndAttendanceData calculateTotalPercentage(
      List<KMData> gradeWisePerformanceResponse, Double attendanceCount, Double knowledgeCount) {
    double knowledgePercentage = 0.0;
    double sectionAttendance = 0.0;
    double totalSectionPercentage = 0.0;
    double totalKnowledgePercentage = 0.0;
    MlpKnowledgeAndAttendanceData mlp = new MlpKnowledgeAndAttendanceData();
    if (!gradeWisePerformanceResponse.isEmpty()) {
      for (var data : gradeWisePerformanceResponse) {
        knowledgePercentage +=
            data.getPercentage().equals("-") ? 0 : Double.parseDouble(data.getPercentage());
        sectionAttendance +=
            data.getAttendance().equals("-") ? 0 : Double.parseDouble(data.getAttendance());
      }
      totalSectionPercentage =
          sectionAttendance == 0.0 ? 0.0 : (sectionAttendance / attendanceCount);
      totalKnowledgePercentage =
          knowledgePercentage == 0.0 ? 0.0 : (knowledgePercentage / knowledgeCount);
    }
    mlp.setAttendancePercentage(totalSectionPercentage);
    mlp.setKnowledgePercentage(totalKnowledgePercentage);

    return mlp;
  }

  public ChapterSummaryDto getSubtopicWisePerformanceOfStudent(
      String authUserId, List<String> chapterSlugs) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    Student student = studentRepository.findByUserId(user.getId());
    var studentAttendanceList =
        kMeterRepository.getSubTopicWiseAttendancePercentage(student.getId(), chapterSlugs);
    var studentKnowledgeList =
        kMeterRepository.getSubTopicWiseKnowledgePercentage(student.getId(), chapterSlugs);

    List<GenericDto> subjectWisePerformanceResponse = new ArrayList<>();

    Double knowledgePercentage = 0.0;
    Double attendancePercentage;

    if (studentAttendanceList.isEmpty()) {
      return ChapterSummaryDto.builder()
          .name(chapterSlugs.getFirst())
          .slug(chapterSlugs.getFirst())
          .build();
    }
    String chapterName = studentAttendanceList.get(0).getChapterName();
    String subjectName = studentAttendanceList.get(0).getSubjectName();
    for (var s : studentAttendanceList) {
      List<ExamRecords> examRecords = new ArrayList<>();

      var knowledgeData =
          studentKnowledgeList.stream()
              .filter(d -> d.getSubtopicName().equals(s.getSubtopicName()))
              .findFirst();

      if (knowledgeData.isPresent()) {
        knowledgePercentage =
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(knowledgeData.get().getKnowledgePercentage()));
        List<ExamRecordsQueryResult> queryResults =
            mlpRepository.getMlpRecordsForSubtopic(
                s.getSubtopicSlug(),
                student.getId(),
                PageRequest.of(0, 1, Sort.by("examId").descending()));

        examRecords =
            queryResults.stream()
                .map(ExamRecordsMapper.mapper::examRecordsQueryResultToDto)
                .toList();
      }

      attendancePercentage =
          Double.parseDouble(Constants.DECIMAL_FORMAT.format(s.getAttendancePercentage()));
      subjectWisePerformanceResponse.add(
          GenericDto.builder()
              .name(Objects.isNull(s.getSubtopicName()) ? Strings.EMPTY : s.getSubtopicName())
              .slug(Objects.isNull(s.getSubtopicSlug()) ? Strings.EMPTY : s.getSubtopicSlug())
              .average(knowledgePercentage)
              .attendance(attendancePercentage)
              .examRecords(examRecords)
              .build());
    }

    return ChapterSummaryDto.builder()
        .slug(chapterSlugs.getFirst())
        .subjectName(subjectName)
        .name(chapterName)
        .subtopicData(subjectWisePerformanceResponse)
        .build();
  }

  public SubjectSummaryBuilder getChapterWisePerformanceByStudent(
      String authUserId, List<String> subject) {
    try {
      User user = userRepository.getUserByAuthUserId(authUserId);
      Student student = studentRepository.findByUserId(user.getId());
      List<GenericDto> chapterWiseStudentPerformance = new ArrayList<>();

      var chapterWiseAttendanceList =
          kMeterRepository.getChapterWiseAttendancePercentage(student.getId(), subject.getFirst());
      var chapterWiseKnowledgeList =
          kMeterRepository.getChapterWiseKnowledgePercentage(student.getId(), subject.getFirst());

      if (chapterWiseAttendanceList.isEmpty()) {
        return SubjectSummaryBuilder.builder()
            .name(subject.getFirst())
            .slug(subject.getFirst())
            .chapterData(chapterWiseStudentPerformance)
            .build();
      }

      for (var chapterData : chapterWiseAttendanceList) {
        Double knowledgePercentage = 0.0;
        Double attendancePercentage;
        var knowledgeData =
            chapterWiseKnowledgeList.stream()
                .filter(d -> d.getChapterName().equals(chapterData.getChapterName()))
                .findFirst();

        if (knowledgeData.isPresent()) {
          knowledgePercentage =
              Double.parseDouble(
                  Constants.DECIMAL_FORMAT.format(knowledgeData.get().getKnowledgePercentage()));
        }
        attendancePercentage =
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(chapterData.getAttendancePercentage()));

        chapterWiseStudentPerformance.add(
            GenericDto.builder()
                .name(chapterData.getChapterName())
                .slug(chapterData.getChapterSlug())
                .average(knowledgePercentage)
                .attendance(attendancePercentage)
                .build());
      }

      return SubjectSummaryBuilder.builder()
          .slug(subject.getFirst())
          .name(chapterWiseAttendanceList.get(0).getSubjectName())
          .chapterData(chapterWiseStudentPerformance)
          .build();
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "error.KnowledgeSubjectDetailsNotFound",
          new String[] {String.join(",", subject)},
          e);
    }
  }

  public StudentKMeterDto.StudentKMeterResponse getStudentKMeter(
      String authUserId, Long examType, String subjectSlug) {

    User user = userRepository.getUserByAuthUserId(authUserId);
    Student student = studentRepository.findByUserId(user.getId());

    var kmeterBySubjects =
        studentKMeterRepository.findAllByStudentIdAndSubjectSlugAndExamType(
            student.getId(), subjectSlug, examType);
    if (kmeterBySubjects.isEmpty()) {
      return StudentKMeterDto.StudentKMeterResponse.builder()
          .subjectSlug(null)
          .subjectName(null)
          .subjectKMeterPercentage(null)
          .chapterKMeter(Collections.emptyList())
          .build();
    }

    var chapterKm = buildChapterKmeter(kmeterBySubjects);
    var chapterPercentage =
        chapterKm.stream()
            .mapToDouble(StudentKMeterDto.StudentKMeterResponseByChapter::chapterKMeterPercentage)
            .sum();
    return StudentKMeterDto.StudentKMeterResponse.builder()
        .subjectSlug(kmeterBySubjects.get(0).getSubjectSlug())
        .subjectName(kmeterBySubjects.get(0).getSubjectName())
        .subjectKMeterPercentage(
            Double.parseDouble(
                Constants.DECIMAL_FORMAT.format(chapterPercentage / chapterKm.size())))
        .chapterKMeter(chapterKm)
        .build();
  }

  private List<StudentKMeterDto.StudentKMeterResponseByChapter> buildChapterKmeter(
      List<StudentKMeter> studentKMeters) {

    var chapterKMeters =
        studentKMeters.stream()
            .collect(
                collectingAndThen(
                    toCollection(
                        () -> new TreeSet<>(Comparator.comparing(StudentKMeter::getChapterSlug))),
                    ArrayList::new));

    return chapterKMeters.stream()
        .map(
            studentKMeter ->
                StudentKMeterDto.StudentKMeterResponseByChapter.builder()
                    .chapterSlug(studentKMeter.getChapterSlug())
                    .chapterName(studentKMeter.getChapterName())
                    .examID(studentKMeter.getExamId())
                    .chapterKMeterPercentage(
                        calculateKMeterPercentage(
                            studentKMeters.stream()
                                .filter(
                                    km ->
                                        km.getChapterSlug().equals(studentKMeter.getChapterSlug()))
                                .toList()))
                    .subTopicKMeter(buildSubtopicKMeter(studentKMeters, studentKMeter))
                    .build())
        .toList();
  }

  private List<StudentKMeterDto.StudentKMeterResponseBySubTopic> buildSubtopicKMeter(
      List<StudentKMeter> studentKMeters, StudentKMeter studentKMeter) {

    if (Constants.TEST_EXAM == studentKMeter.getExamType()) {
      return Collections.emptyList();
    }

    return new ArrayList<>(
        studentKMeters.stream()
            .filter(km -> studentKMeter.getChapterSlug().equals(km.getChapterSlug()))
            .map(
                studentSubTopicKMeter ->
                    StudentKMeterDto.StudentKMeterResponseBySubTopic.builder()
                        .subTopicSlug(studentSubTopicKMeter.getSubTopicSlug())
                        .subTopicName(studentSubTopicKMeter.getSubTopicName())
                        .subTopicKMeterPercentage(
                            Double.parseDouble(
                                Constants.DECIMAL_FORMAT.format(
                                    studentSubTopicKMeter.getKnowledgePercentage())))
                        .examID(studentSubTopicKMeter.getExamId())
                        .build())
            .toList());
  }

  public Double calculateKMeterPercentage(List<StudentKMeter> studentKMeters) {

    var kmeterPercenatage =
        studentKMeters.stream().mapToDouble(StudentKMeter::getKnowledgePercentage).sum()
            / studentKMeters.size();
    return Double.parseDouble(Constants.DECIMAL_FORMAT.format(kmeterPercenatage));
  }

  public List<GenericMetricResponse> getAllStudentsKMSummaryByGrade(
      String orgSlug, List<String> subjects, String boardSlug, String gradeSlug) {
    var allStudentsInGrade =
        studentRepository.findByGradeAndOrg(Collections.singletonList(gradeSlug), orgSlug);
    var filteredStudentsByBoard =
        allStudentsInGrade.stream()
            .filter(s -> s.getSection().getBoardSlug().equals(boardSlug))
            .toList();
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();

    for (Student student : filteredStudentsByBoard) {
      Map<String, Object> dataMap = new HashMap<>();
      var kMSummaryForStudent =
          getSubjectWisePerformanceOfStudent(student.getUserInfo().getAuthUserId(), subjects);

      dataMap.put(
          "Average_attendance_percentage", kMSummaryForStudent.getAverageAttendancePercentage());
      dataMap.put(
          "Average_knowledge_percentage", kMSummaryForStudent.getAverageKnowledgePercentage());
      dataMap.put("student_id", student.getId());
      dataMap.put("auth_user_id", student.getUserInfo().getAuthUserId());
      dataMap.put("first_name", student.getUserInfo().getFirstName());
      dataMap.put("last_name", student.getUserInfo().getLastName());

      genericMetricResponses.add(GenericMetricResponse.builder().data(dataMap).build());
    }
    return genericMetricResponses;
  }
}
