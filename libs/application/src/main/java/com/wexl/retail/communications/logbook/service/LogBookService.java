package com.wexl.retail.communications.logbook.service;

import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.communications.logbook.dto.LogBookDto;
import com.wexl.retail.communications.logbook.model.LogBook;
import com.wexl.retail.communications.logbook.repository.LogBookRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LogBookService {
  private final UserRepository userRepository;
  private final LogBookRepository logBookRepository;
  private final ValidationUtils validationUtils;
  private final StrapiService strapiService;

  public void saveLogBook(String orgSlug, LogBookDto.Request request, String teacherAuthId) {
    var teacher = userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug);
    List<LogBook> logBooks = new ArrayList<>();
    request
        .studentIds()
        .forEach(
            student ->
                logBooks.add(
                    LogBook.builder()
                        .title(request.title())
                        .description(request.description())
                        .boardSlug(request.boardSlug())
                        .gradeSlug(request.gradeSlug())
                        .orgSlug(orgSlug)
                        .studentId(student)
                        .attachments(request.attachment())
                        .sectionUuid(request.sectionUuids())
                        .type(request.type())
                        .teacher(teacher)
                        .build()));

    logBookRepository.saveAll(logBooks);
  }

  public List<LogBookDto.Response> getLogBook(
      String orgSlug,
      String teacherAuthId,
      List<String> section,
      List<String> gradeSlug,
      List<String> sectionUuids,
      List<Long> studentIds) {

    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();

    List<LogBook> logBookList =
        UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())
            ? logBookRepository.getLongBookData(
                orgSlug, section, gradeSlug, sectionUuids, studentIds, null)
            : logBookRepository.getLongBookData(
                orgSlug,
                section,
                gradeSlug,
                sectionUuids,
                studentIds,
                teacher.getUserInfo().getId());
    var allGrades = strapiService.getAllGrades();
    var allEduBoards = strapiService.getAllBoards();
    return logBookList.stream()
        .map(
            logBook -> {
              Student student = validationUtils.isStudentValid(logBook.getStudentId());
              String studentName =
                  student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();

              User logBookTeacher = logBook.getTeacher();
              String teacherName =
                  logBookTeacher.getFirstName() + " " + logBookTeacher.getLastName();
              var board = validationUtils.findBoardBySlug(allEduBoards, logBook.getBoardSlug());
              var grade = validationUtils.findGradeBySlug(allGrades, logBook.getGradeSlug());
              var sectionData = validationUtils.findSectionByUuid(logBook.getSectionUuid());
              return new LogBookDto.Response(
                  logBook.getId(),
                  logBook.getTitle(),
                  logBook.getDescription(),
                  logBook.getGradeSlug(),
                  grade.getName(),
                  logBook.getBoardSlug(),
                  board.getAssetName(),
                  logBook.getStudentId(),
                  studentName,
                  logBook.getSectionUuid(),
                  sectionData.getName(),
                  logBookTeacher.getId(),
                  teacherName,
                  logBook.getCreatedAt().getTime(),
                  logBook.getAttachments(),
                  logBook.getType());
            })
        .toList();
  }

  public void updateLogBook(
      String orgSlug, Long logBookId, LogBookDto.Request request, String teacherAuthId) {
    var teacher = userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug);
    var logBook = getLogBook(logBookId);
    logBook.setTitle(request.title());
    logBook.setDescription(request.description());
    logBook.setType(request.type());
    logBook.setSectionUuid(request.sectionUuids());
    logBook.setAttachments(logBook.getAttachments());
    logBook.setBoardSlug(request.boardSlug());
    logBook.setGradeSlug(request.gradeSlug());
    logBook.setTeacher(teacher);

    logBookRepository.save(logBook);
  }

  private LogBook getLogBook(Long logBookId) {
    var logBook = logBookRepository.findById(logBookId);
    if (logBook.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.LogBookId",
          new String[] {Long.toString(logBookId)});
    }

    return logBook.get();
  }

  public void deleteLogBook(Long logBookId) {
    var logBook = getLogBook(logBookId);
    logBookRepository.delete(logBook);
  }
}
