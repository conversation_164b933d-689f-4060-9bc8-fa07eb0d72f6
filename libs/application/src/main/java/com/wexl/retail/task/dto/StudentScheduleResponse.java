package com.wexl.retail.task.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StudentScheduleResponse {

  @JsonProperty("classroom_name")
  private String classroomName;

  @JsonProperty("display_name")
  private String displayName;

  @JsonProperty("classroom_id")
  private Long classroomId;

  @JsonProperty("schedule_id")
  private Long scheduleId;

  @JsonProperty("schedule_inst_id")
  private Long scheduleInstId;

  @JsonProperty("schedule_inst_name")
  private String scheduleInstName;

  @JsonProperty("start_time")
  private Long startTime;

  @JsonProperty("end_time")
  private Long endTime;

  @JsonProperty("status")
  private String status;

  @JsonProperty("meeting_id")
  private Long meetingId;

  @JsonProperty("meeting_name")
  private String meetingName;

  @JsonProperty("meeting_link")
  private String meetingLink;

  @JsonProperty("day_of_week")
  private String dayOfWeek;

  private String attendance;
}
